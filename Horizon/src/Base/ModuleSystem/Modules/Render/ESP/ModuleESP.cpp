#include "pch.h"
#include "ModuleESP.h"

#include <codecvt>

#include "Renderer/Renderer.h"

namespace Base
{
	void ModuleESP::SetupSettings()
	{
		// ===== GENERAL SETTINGS =====
		AddSetting(new SliderSetting(XOR("Text Size"), m_general.textScale, 1.0, 10.0, 1));
		AddSetting(new SliderSetting(XOR("ESP Range"), m_general.espRange, 0, 1000, 2));

		// ===== PLAYER SETTINGS =====
		AddSetting(new CheckboxSetting(XOR("Friendly Players"), m_player.friendly, 10));
		AddSetting(new CheckboxSetting(XOR("Enemy Players"), m_player.enemy, 11));
		AddSetting(new CheckboxSetting(XOR("Sleeping Players"), m_player.sleeping, 12));
		AddSetting(new CheckboxSetting(XOR("Dead Players"), m_player.dead, 13));
		AddSetting(new CheckboxSetting(XOR("Extended Player Info"), m_player.extendedInfo, 14));
		AddSetting(new CheckboxSetting(XOR("Hide Player Health"), m_player.hideHealth, 15));
		AddSetting(new CheckboxSetting(XOR("Hide Player Tribe"), m_player.hideTribe, 16));

		// ===== DINO SETTINGS =====
		AddSetting(new CheckboxSetting(XOR("Friendly Dinos"), m_dino.friendly, 20));
		AddSetting(new CheckboxSetting(XOR("Enemy Dinos"), m_dino.enemy, 21));
		AddSetting(new CheckboxSetting(XOR("Wild Dinos"), m_dino.wild, 22));
		AddSetting(new CheckboxSetting(XOR("Dead Dinos"), m_dino.dead, 23));
		AddSetting(new CheckboxSetting(XOR("Alpha Dinos"), m_dino.alpha, 24));
		AddSetting(new CheckboxSetting(XOR("Filter By Level"), m_dino.levelFilter, 25));
		AddSetting(new CheckboxSetting(XOR("Hide Dino Level"), m_dino.hideLevel, 26));
		AddSetting(new CheckboxSetting(XOR("Hide Dino Aggression"), m_dino.hideAggression, 27));
		AddSetting(new CheckboxSetting(XOR("Use Dino Name Filter"), m_dino.nameFilter, 28));
		AddSetting(new SliderSetting(XOR("Min Level Filter"), m_dino.minLevel, 0.0, 300.0, 3));

		// ===== DINO STATS =====
		AddSetting(new CheckboxSetting(XOR("Show Stamina"), m_dino.showStamina, 30));
		AddSetting(new CheckboxSetting(XOR("Show Oxygen"), m_dino.showOxygen, 31));
		AddSetting(new CheckboxSetting(XOR("Show Food"), m_dino.showFood, 32));
		AddSetting(new CheckboxSetting(XOR("Show Weight"), m_dino.showWeight, 33));
		AddSetting(new CheckboxSetting(XOR("Show Melee Damage"), m_dino.showMeleeDamage, 34));
		AddSetting(new CheckboxSetting(XOR("Show Movement Speed"), m_dino.showMovementSpeed, 35));
		AddSetting(new CheckboxSetting(XOR("Show Torpor"), m_dino.showTorpor, 36));

		// ===== STRUCTURE SETTINGS =====
		AddSetting(new CheckboxSetting(XOR("Vaults"), m_structure.vault, 40));
		AddSetting(new CheckboxSetting(XOR("Dedicated Storage"), m_structure.dedicatedStorage, 41));
		AddSetting(new CheckboxSetting(XOR("Small Storage Box"), m_structure.storageBoxSmall, 42));
		AddSetting(new CheckboxSetting(XOR("Large Storage Box"), m_structure.storageBoxLarge, 43));
		AddSetting(new CheckboxSetting(XOR("Supply Crates"), m_structure.supplyCrate, 44));
		AddSetting(new CheckboxSetting(XOR("Turrets"), m_structure.turret, 45));
		AddSetting(new CheckboxSetting(XOR("Beds"), m_structure.bed, 46));
		AddSetting(new CheckboxSetting(XOR("Tek Generator"), m_structure.tekGenerator, 47));
		AddSetting(new CheckboxSetting(XOR("Electric Generator"), m_structure.electricGenerator, 48));
		AddSetting(new CheckboxSetting(XOR("Cryo Fridge"), m_structure.cryoFridge, 49));
		AddSetting(new CheckboxSetting(XOR("Loadout Dummy"), m_structure.loadoutDummy, 50));
		AddSetting(new CheckboxSetting(XOR("Ammo Box"), m_structure.ammoBox, 51));
		AddSetting(new CheckboxSetting(XOR("Tek Sensor"), m_structure.tekSensor, 52));
		AddSetting(new CheckboxSetting(XOR("Hide Empty Containers"), m_structure.hideEmpty, 53));
		AddSetting(new CheckboxSetting(XOR("Hide Item Amount"), m_structure.hideItemAmount, 54));
		AddSetting(new CheckboxSetting(XOR("Hide Structure Health"), m_structure.hideHealth, 55));
		AddSetting(new CheckboxSetting(XOR("Hide Turret Settings"), m_structure.hideTurretSettings, 56));
		AddSetting(new CheckboxSetting(XOR("Hide Friendly Structures"), m_structure.hideFriendly, 57));
		AddSetting(new CheckboxSetting(XOR("Hide Enemy Structures"), m_structure.hideEnemy, 58));

		// ===== ITEM SETTINGS =====
		AddSetting(new CheckboxSetting(XOR("Items"), m_item.items, 60));
		AddSetting(new CheckboxSetting(XOR("Item Caches"), m_item.itemCache, 61));
		AddSetting(new CheckboxSetting(XOR("Eggs"), m_item.eggs, 62));

		// ===== RESOURCE ESP =====
		AddSetting(new CheckboxSetting(XOR("Enable Resource ESP"), m_resource.enabled, 70));
		AddSetting(new CheckboxSetting(XOR("Show Metal"), m_resource.metal, 71));
		AddSetting(new CheckboxSetting(XOR("Show Oil"), m_resource.oil, 72));
		AddSetting(new CheckboxSetting(XOR("Show Crystal"), m_resource.crystal, 73));
		AddSetting(new CheckboxSetting(XOR("Show Obsidian"), m_resource.obsidian, 74));
		AddSetting(new CheckboxSetting(XOR("Show Silica"), m_resource.silica, 75));
		AddSetting(new SliderSetting(XOR("Resource Render Distance"), m_resource.renderDistance, 0.0, 150000.0, 4));
		AddSetting(new SliderSetting(XOR("Resource Refresh Rate (Seconds)"), m_resource.reloadInterval, 1.0, 60.0, 5));

		// ===== MISC SETTINGS =====
		AddSetting(new CheckboxSetting(XOR("Player Spawnpoints"), m_misc.showSpawnpoints, 80));
		AddSetting(new CheckboxSetting(XOR("Explorer Notes"), m_misc.explorerNotes, 81));
		AddSetting(new CheckboxSetting(XOR("Unlocked Only"), m_misc.unlockedOnly, 82));
		AddSetting(new CheckboxSetting(XOR("Limit Render Distance"), m_misc.exRender, 83));

		// ===== COLORS =====
		AddSetting(new ColorsSetting(XOR("Structure Color"), XOR("#9900ff"), 1));
		AddSetting(new ColorsSetting(XOR("Friendly Dino Color"), XOR("#0099cc"), 2));
		AddSetting(new ColorsSetting(XOR("Enemy Dino Color"), XOR("#9900cc"), 3));
		AddSetting(new ColorsSetting(XOR("Friendly Player Color"), XOR("#0066ff"), 4));
		AddSetting(new ColorsSetting(XOR("Enemy Player Color"), XOR("#6600ff"), 5));
		AddSetting(new ColorsSetting(XOR("Health Color"), XOR("#ff3300"), 6));
		AddSetting(new ColorsSetting(XOR("Dino Stats Color"), XOR("#006aff"), 7));

		// ===== DINO NAME FILTERS =====
		for (int i = 0; i < 8; ++i)
		{
			AddSetting(new TextboxSetting(XOR("Dino Name Filter ") + std::to_string(i + 1), "", i + 1));
		}
	}

	void ModuleESP::LoadSettings(nlohmann::json config)
	{
		// Load settings from config - simplified implementation
		// In a full implementation, you would load all the structured settings here
		// For now, we'll use default values set in the header
	}

	const uint8_t LEFT = 0;
	const uint8_t CENTER = 1;
	const uint8_t RIGHT = 2;
	static bool InitializedResourceThread = false;

	struct FOverlappedFoliageElementCached // : public CCG::ShooterGame::FOverlappedFoliageElement
	{
		std::wstring CachedDescriptiveNameW;
		CG::CoreUObject::FVector CachedHarvestLocation;
		// Rest of your structure fields...
	};

	std::string Aggression[] = { XOR("Passive"), XOR("Neutral"), XOR("Aggressive"), XOR("Attack YOUR Target") };
	std::string TurretRange[] = { XOR("Low"), XOR("Medium"), XOR("High") };
	std::string TurretTarget[] = { XOR("All Targets"), XOR("Only Survivors or Tames Creatures"), XOR("Only Survivors"), XOR("Only Wild Creatures"), XOR("Only Tamed Creatures"), XOR("Only Survivors & Mounted Creatures") };

	void ModuleESP::DrawText_(CG::Engine::AHUD* hud, CG::BasicTypes::FString text, float x, float y, uint8_t alignment, CG::CoreUObject::FLinearColor color, CG::Engine::UFont* font, float scale)
	{
		float width;
		float height;

		hud->GetTextSize(text, &width, &height, font, scale);

		switch (alignment)
		{
		case CENTER:
			x -= width / 2;
			break;
		case RIGHT:
			x -= width;
			break;
		}

		CG::CoreUObject::FLinearColor outlineColor = { 0.f, 0.f, 0.f, 1.f };

		// Draw outline
		/*hud->DrawText(text, outlineColor, x - 1.0f, y, font, scale, false);
		hud->DrawText(text, outlineColor, x + 1.0f, y, font, scale, false);
		hud->DrawText(text, outlineColor, x, y - 1.0f, font, scale, false);
		hud->DrawText(text, outlineColor, x, y + 1.0f, font, scale, false);

		hud->DrawText(text, color, x, y, font, scale, false);*/
		hud->Canvas->K2_DrawText(font, text, {x,y}, {scale,scale}, color, 1.f, {0,0,0,0}, {0,0}, 0, 0, 1, {0,0,0,0.5});
		//hud->Canvas->K2_DrawText(font, text, { x, y }, color, 0.f, { 0.f, 0.f, 0.f, 0.f }, { 0.f, 0.f }, false, false, true, { 0.f, 0.f, 0.f, 0.f });
	}

	void ModuleESP::DrawTextY(CG::Engine::AHUD* HUD, CG::BasicTypes::FString text, float x, float y, uint8_t alignment, CG::CoreUObject::FLinearColor color, CG::Engine::UFont* font, float scale, float* yValue)
	{
		float width;
		float height;

		CG::CoreUObject::FVector2D screenPos = { x, y };

		if (Renderer::IsInScreen(screenPos))
		{
			HUD->GetTextSize(text, &width, &height, font, scale);

			if (text.IsValid() && text.ToString().length() != 0)
			{
				DrawText_(HUD, text, x, y, alignment, color, font, scale);
				*yValue += height / 2 + 2;
			}
		}
	}

	void ModuleESP::RenderDino(const RenderContext& ctx, CG::ShooterGame::APrimalDinoCharacter* dino)
	{
		if (!dino) return;

		auto screenPos = Renderer::WorldToScreen(dino->K2_GetActorLocation(), ctx.camLoc, ctx.camRot, ctx.camFov);
		if (!Renderer::IsInScreen(screenPos)) return;

		// Get basic info
		std::wstring dinoName = dino->DescriptiveName.IsValid() ? dino->DescriptiveName.ToWString() : L"Unknown";
		std::wstring tribeName = dino->TribeName.IsValid() ? dino->TribeName.ToWString() : L"No Tribe";

		float distance = GetDistance(ctx, dino);
		bool isAllied = dino->TargetingTeam == ctx.playerController->TargetingTeam;
		bool isTamed = dino->BPIsTamed();

		// Clean up dino name
		if (dinoName.find(L"a ") == 0) dinoName.erase(0, 2);
		dinoName += L" [" + std::to_wstring(static_cast<int>(distance)) + L"m]";

		// Add level if available and not hidden
		if (!m_dino.hideLevel && dino->GetCharacterStatusComponent())
		{
			auto level = dino->GetCharacterStatusComponent()->GetCharacterLevel();
			if (level > 0) dinoName += L" [Level " + std::to_wstring(level) + L"]";
		}

		// Determine color
		CG::CoreUObject::FLinearColor color = isAllied ? m_colors.dino : m_colors.enemyDino;

		float y = screenPos.Y;

		// Health and aggression info
		std::wstring healthAggression;
		if (!m_player.hideHealth) // Using player settings for now, should be dino settings
		{
			float health = dino->GetHealth();
			float maxHealth = dino->GetMaxHealth();
			healthAggression = std::to_wstring(static_cast<int>(health)) + L"/" + std::to_wstring(static_cast<int>(maxHealth)) + L" HP ";
		}

		if (!m_dino.hideAggression && isTamed && dino->TamedAggressionLevel < 4)
		{
			std::string aggressionText[] = {"Passive", "Neutral", "Aggressive", "Attack YOUR Target"};
			healthAggression += L"[" + Global::ConvertUtf8ToUtf162(aggressionText[dino->TamedAggressionLevel]) + L"]";
		}

		if (!healthAggression.empty())
		{
			float healthPercent = dino->GetHealth() / dino->GetMaxHealth() * 100.0f;
			CG::CoreUObject::FLinearColor healthColor = m_colors.health;
			if (healthPercent <= 25) healthColor.A = 0.8f;
			else if (healthPercent <= 50) healthColor.A = 0.6f;

			DrawTextY(ctx.hud, healthAggression.c_str(), screenPos.X, y, 1, healthColor, ctx.font, m_general.textScale, &y);
		}

		// Dino stats (only for tamed or knocked out dinos)
		auto statusComp = dino->GetCharacterStatusComponent();
		if (statusComp && (isTamed || dino->ReplicatedCurrentTorpor > 0))
		{
			std::wstring dinoStats;

			if (m_dino.showStamina)
				dinoStats += L"[Stamina: " + std::to_wstring(static_cast<int>(statusComp->BPGetCurrentStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Stamina))) + L"/" + std::to_wstring(static_cast<int>(statusComp->BPGetMaxStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Stamina))) + L"] ";

			if (m_dino.showOxygen)
				dinoStats += L"[Oxygen: " + std::to_wstring(static_cast<int>(statusComp->BPGetCurrentStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Oxygen))) + L"/" + std::to_wstring(static_cast<int>(statusComp->BPGetMaxStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Oxygen))) + L"] ";

			if (m_dino.showFood)
				dinoStats += L"[Food: " + std::to_wstring(static_cast<int>(statusComp->BPGetCurrentStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Food))) + L"/" + std::to_wstring(static_cast<int>(statusComp->BPGetMaxStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Food))) + L"] ";

			if (m_dino.showWeight)
				dinoStats += L"[Weight: " + std::to_wstring(static_cast<int>(statusComp->BPGetCurrentStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Weight))) + L"/" + std::to_wstring(static_cast<int>(statusComp->BPGetMaxStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Weight))) + L"] ";

			if (m_dino.showMeleeDamage)
				dinoStats += L"[Melee: " + std::to_wstring(static_cast<int>(statusComp->BPGetCurrentStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::MeleeDamageMultiplier))) + L"/" + std::to_wstring(static_cast<int>(statusComp->BPGetMaxStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::MeleeDamageMultiplier))) + L"] ";

			if (m_dino.showMovementSpeed)
				dinoStats += L"[Speed: " + std::to_wstring(static_cast<int>(statusComp->BPGetCurrentStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::SpeedMultiplier))) + L"/" + std::to_wstring(static_cast<int>(statusComp->BPGetMaxStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::SpeedMultiplier))) + L"] ";

			if (m_dino.showTorpor)
				dinoStats += L"[Torpor: " + std::to_wstring(dino->ReplicatedCurrentTorpor) + L"/" + std::to_wstring(dino->ReplicatedMaxTorpor) + L"] ";

			if (!dinoStats.empty())
			{
				DrawTextY(ctx.hud, dinoStats.c_str(), screenPos.X, y, 1, m_colors.dinoStats, ctx.font, m_general.textScale, &y);
			}
		}

		// Main dino name
		DrawTextY(ctx.hud, dinoName.c_str(), screenPos.X, y, 1, color, ctx.font, m_general.textScale, &y);

		// Tribe info for tamed dinos
		if (!m_player.hideTribe && isTamed) // Using player settings for now
		{
			DrawTextY(ctx.hud, tribeName.c_str(), screenPos.X, y, 1, color, ctx.font, m_general.textScale, &y);

			if (dino->UploadedFromServerName.IsValid())
			{
				std::wstring tamedServer = dino->UploadedFromServerName.ToWString();
				std::erase(tamedServer, '\n');
				DrawTextY(ctx.hud, tamedServer.c_str(), screenPos.X, y, 1, color, ctx.font, m_general.textScale, &y);
			}
		}
	}

	void ModuleESP::RenderStructure(const RenderContext& ctx, CG::Engine::AActor* actor)
	{
		if (!actor) return;

		auto screenPos = Renderer::WorldToScreen(actor->K2_GetActorLocation(), ctx.camLoc, ctx.camRot, ctx.camFov);
		if (!Renderer::IsInScreen(screenPos)) return;

		std::wstring structureName = L"Structure";
		float distance = GetDistance(ctx, actor);
		bool isAllied = actor->TargetingTeam == ctx.playerController->TargetingTeam;

		// Determine structure type and name
		if (FNameCache::IsA(actor, FNameCache::Containers::StorageBoxHuge))
			structureName = L"Vault";
		else if (FNameCache::IsA(actor, FNameCache::Containers::DedicatedStorage))
			structureName = L"Dedicated Storage";
		else if (FNameCache::IsA(actor, FNameCache::Containers::StorageBoxSmall))
			structureName = L"Small Storage";
		else if (FNameCache::IsA(actor, FNameCache::Containers::StorageBoxLarge))
			structureName = L"Large Storage";
		else if (FNameCache::IsA(actor, FNameCache::Actors::PrimalStructureItemContainerSupplyCrate))
			structureName = L"Supply Crate";
		else if (FNameCache::IsA(actor, FNameCache::Actors::PrimalStructureTurret))
			structureName = L"Turret";
		else if (FNameCache::IsA(actor, FNameCache::Actors::PrimalStructureBed))
			structureName = L"Bed";
		else if (FNameCache::IsA(actor, FNameCache::Containers::TekGenerator))
			structureName = L"Tek Generator";
		else if (FNameCache::IsA(actor, FNameCache::Containers::ElectricGenerator))
			structureName = L"Electric Generator";
		else if (FNameCache::IsA(actor, FNameCache::Containers::CryoFridge))
			structureName = L"Cryo Fridge";
		else if (FNameCache::IsA(actor, FNameCache::Containers::AmmoContainer))
			structureName = L"Ammo Box";

		structureName += L" [" + std::to_wstring(static_cast<int>(distance)) + L"m]";

		float y = screenPos.Y;
		DrawTextY(ctx.hud, structureName.c_str(), screenPos.X, y, 1, m_colors.structure, ctx.font, m_general.textScale, &y);
	}

	void ModuleESP::RenderItem(const RenderContext& ctx, CG::Engine::AActor* item)
	{
		if (!item) return;

		auto screenPos = Renderer::WorldToScreen(item->K2_GetActorLocation(), ctx.camLoc, ctx.camRot, ctx.camFov);
		if (!Renderer::IsInScreen(screenPos)) return;

		std::wstring itemName = L"Item";
		float distance = GetDistance(ctx, item);

		if (FNameCache::IsA(item, FNameCache::Actors::DroppedItemEgg))
			itemName = L"Egg";
		else if (FNameCache::IsA(item, FNameCache::Actors::DroppedItemGeneric))
			itemName = L"Dropped Item";
		else if (FNameCache::IsA(item, FNameCache::Containers::DeathItemCache))
			itemName = L"Death Cache";

		itemName += L" [" + std::to_wstring(static_cast<int>(distance)) + L"m]";

		float y = screenPos.Y;
		DrawTextY(ctx.hud, itemName.c_str(), screenPos.X, y, 1, m_colors.structure, ctx.font, m_general.textScale, &y);
	}

	void ModuleESP::RenderPlayer(const RenderContext& ctx, CG::ShooterGame::AShooterCharacter* player)
	{
		if (!player) return;

		auto screenPos = Renderer::WorldToScreen(player->K2_GetActorLocation(), ctx.camLoc, ctx.camRot, ctx.camFov);
		if (!Global::IsInScreen(screenPos, Global::ScreenWidth, Global::ScreenHeight)) return;

		// Get player info
		std::wstring playerName = player->PlayerName.IsValid() ? player->PlayerName.ToWString() : L"Unknown";
		std::wstring tribeName = player->TribeName.IsValid() ? player->TribeName.ToWString() : L"No Tribe";
		std::wstring implantId = player->LinkedPlayerIDString().IsValid() ? player->LinkedPlayerIDString().ToWString() : L"-";
		std::wstring platformName = player->PlatformProfileName.IsValid() ? player->PlatformProfileName.ToWString() : L"-";

		int distance = static_cast<int>(GetDistance(ctx, player));
		bool isAllied = player->TargetingTeam == ctx.playerController->TargetingTeam;
		bool isDead = player->IsDead();

		// Determine color
		CG::CoreUObject::FLinearColor color = isAllied ? m_colors.player : m_colors.enemyPlayer;
		if (isDead) color = {0.3f, 0.3f, 0.3f, 1.0f};

		float y = screenPos.Y;

		// Main player info
		auto level = player->GetCharacterStatusComponent() ? player->GetCharacterStatusComponent()->GetCharacterLevel() : 0;
		std::wstring mainText = playerName + L" - Lvl " + std::to_wstring(level) + L" [" + std::to_wstring(distance) + L"m]";
		DrawTextY(ctx.hud, mainText.c_str(), screenPos.X, y, 1, color, ctx.font, m_general.textScale + 0.1f, &y);

		// Tribe info for enemies
		if (!isAllied && !m_player.hideTribe)
		{
			DrawTextY(ctx.hud, tribeName.c_str(), screenPos.X, y, 1, m_colors.player, ctx.font, m_general.textScale + 0.1f, &y);
		}

		// Extended info for enemies
		if (!isAllied && m_player.extendedInfo)
		{
			DrawTextY(ctx.hud, platformName.c_str(), screenPos.X, y, 1, m_colors.player, ctx.font, m_general.textScale + 0.1f, &y);
			DrawTextY(ctx.hud, implantId.c_str(), screenPos.X, y, 1, m_colors.player, ctx.font, m_general.textScale + 0.1f, &y);
		}

		// Health info
		if (!m_player.hideHealth)
		{
			float health = player->GetHealth();
			float maxHealth = player->GetMaxHealth();
			float healthPercent = (health / maxHealth) * 100.0f;

			CG::CoreUObject::FLinearColor healthColor = m_colors.health;
			if (healthPercent <= 25) healthColor.A = 0.8f;
			else if (healthPercent <= 50) healthColor.A = 0.6f;

			std::wstring healthText = std::to_wstring(static_cast<int>(health)) + L"/" + std::to_wstring(static_cast<int>(maxHealth)) + L" HP";
			DrawTextY(ctx.hud, healthText.c_str(), screenPos.X, y, 1, healthColor, ctx.font, m_general.textScale + 0.1f, &y);
		}
	}

	void ModuleESP::OnPostRender(CG::ShooterGame::UShooterGameViewportClient* viewport, CG::Engine::UCanvas* canvas, CG::Engine::UWorld* world, CG::Engine::UFont* font)
	{
		// Initialize render context
		RenderContext ctx;
		ctx.font = font;
		ctx.playerController = Global::GetPlayerController(world);

		if (!ctx.playerController || !ctx.playerController->PlayerCameraManager || !ctx.playerController->GetPlayerCharacter())
			return;

		ctx.selfPlayer = ctx.playerController->GetPlayerCharacter();
		ctx.hud = ctx.playerController->GetShooterHud();

		if (!ctx.hud || !world->PersistentLevel || !world->PersistentLevel->ActorsList.Count())
			return;

		ctx.hud->Canvas = canvas;
		ctx.camLoc = ctx.playerController->PlayerCameraManager->GetCameraLocation();
		ctx.camRot = ctx.playerController->PlayerCameraManager->GetCameraRotation();
		ctx.camFov = ctx.playerController->PlayerCameraManager->CameraCachePrivate.POV.FOV;

		// Process different ESP types
		ProcessActors(ctx, world);

		if (m_resource.enabled)
			ProcessResourceESP(ctx, world);

		if (m_misc.showSpawnpoints)
			ProcessSpawnpoints(ctx, world);

		if (m_misc.explorerNotes)
			ProcessExplorerNotes(ctx, world);
	}

	void ModuleESP::ProcessActors(const RenderContext& ctx, CG::Engine::UWorld* world)
	{
		auto actorArray = world->PersistentLevel->ActorsList;

		for (int i = 0; i < actorArray.Count(); ++i)
		{
			auto actor = actorArray[i];
			if (!actor || !ShouldRender(ctx, actor))
				continue;

			// Use FNameCache for efficient type checking
			if (FNameCache::IsA(actor, FNameCache::Actors::ShooterCharacter))
			{
				auto player = static_cast<CG::ShooterGame::AShooterCharacter*>(actor);
				if (ShouldRenderPlayer(player))
					RenderPlayer(ctx, player);
			}
			else if (FNameCache::IsA(actor, FNameCache::Actors::PrimalDinoCharacter))
			{
				auto dino = static_cast<CG::ShooterGame::APrimalDinoCharacter*>(actor);
				if (ShouldRenderDino(dino))
					RenderDino(ctx, dino);
			}
			else if (FNameCache::IsA(actor, FNameCache::Actors::PrimalStructure))
			{
				if (ShouldRenderStructure(actor))
					RenderStructure(ctx, actor);
			}
			else if (m_item.items && FNameCache::IsA(actor, FNameCache::Actors::DroppedItemGeneric))
			{
				RenderItem(ctx, actor);
			}
			else if (m_item.eggs && FNameCache::IsA(actor, FNameCache::Actors::DroppedItemEgg))
			{
				RenderItem(ctx, actor);
			}
			else if (m_item.itemCache && FNameCache::IsA(actor, FNameCache::Containers::DeathItemCache))
			{
				RenderStructure(ctx, actor);
			}
		}
	}

	void ModuleESP::ProcessResourceESP(const RenderContext& ctx, CG::Engine::UWorld* world)
	{
		static std::vector<FOverlappedFoliageElementCached> cachedFoliage;
		static std::chrono::steady_clock::time_point lastExecutionTime = std::chrono::steady_clock::now();
		auto currentTime = std::chrono::steady_clock::now();
		auto elapsedTime = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - lastExecutionTime).count();

		if (elapsedTime > (m_resource.reloadInterval * 1000))
		{
			auto victoryCore = Global::GetVictoryCore();
			CG::CoreUObject::FVector origin = ctx.selfPlayer->K2_GetActorLocation();
			CG::BasicTypes::TArray<CG::ShooterGame::FOverlappedFoliageElement> foliage;
			victoryCore->STATIC_ServerSearchFoliage(world, origin, m_resource.renderDistance, &foliage, true, true, true, false, false);

			lastExecutionTime = currentTime;
			cachedFoliage.clear();

			for (int i = 0; i < foliage.Count(); i++)
			{
				std::wstring descName = foliage[i].HarvestingComponent->DescriptiveName.ToWString();

				// Filter unwanted resources
				if (descName.find(L"Bush") != std::wstring::npos) continue;
				if (descName.find(L"Rock") != std::wstring::npos) continue;
				if (descName.find(L"Stone") != std::wstring::npos) continue;
				if (descName.find(L"Tree") != std::wstring::npos) continue;
				if (descName.find(L"Oil") != std::wstring::npos && !m_resource.oil) continue;
				if (descName.find(L"Crystal") != std::wstring::npos && !m_resource.crystal) continue;
				if (descName.find(L"Metal") != std::wstring::npos && !m_resource.metal) continue;
				if (descName.find(L"Obsidian") != std::wstring::npos && !m_resource.obsidian) continue;
				if (descName.find(L"Silica") != std::wstring::npos && !m_resource.silica) continue;

				FOverlappedFoliageElementCached cachedElement;
				cachedElement.CachedDescriptiveNameW = descName;
				cachedElement.CachedHarvestLocation = foliage[i].HarvestLocation;
				cachedFoliage.push_back(cachedElement);
			}
		}

		// Render cached resources
		CG::CoreUObject::FLinearColor color = {0.88f, 0.88f, 0.44f, 1.0f};
		for (const auto& resource : cachedFoliage)
		{
			if (resource.CachedDescriptiveNameW.empty()) continue;

			float distance = Vector3(ctx.camLoc.X, ctx.camLoc.Y, ctx.camLoc.Z)
				.Distance(Vector3(resource.CachedHarvestLocation.X, resource.CachedHarvestLocation.Y, resource.CachedHarvestLocation.Z)) / 100.0f;

			std::wstring resourceLabel = resource.CachedDescriptiveNameW + L" [" + std::to_wstring(static_cast<int>(distance)) + L"m]";
			CG::CoreUObject::FVector2D screenPos = Renderer::WorldToScreen(resource.CachedHarvestLocation, ctx.camLoc, ctx.camRot, ctx.camFov);

			if (Renderer::IsInScreen(screenPos))
			{
				float y = screenPos.Y;
				DrawTextY(ctx.hud, resourceLabel.c_str(), screenPos.X, y, 1, color, ctx.font, m_general.textScale, &y);
			}
		}
	}

	void ModuleESP::ProcessSpawnpoints(const RenderContext& ctx, CG::Engine::UWorld* world)
	{
		// Implementation for spawnpoint ESP
		// This would require specific spawnpoint actor types
	}

	void ModuleESP::ProcessExplorerNotes(const RenderContext& ctx, CG::Engine::UWorld* world)
	{
		auto actorArray = world->PersistentLevel->ActorsList;

		for (int i = 0; i < actorArray.Count(); ++i)
		{
			auto actor = actorArray[i];
			if (!actor) continue;

			if (FNameCache::IsA(actor, FNameCache::Actors::ExplorerChestBase))
			{
				auto screenPos = Renderer::WorldToScreen(actor->K2_GetActorLocation(), ctx.camLoc, ctx.camRot, ctx.camFov);
				if (!Renderer::IsInScreen(screenPos)) continue;

				float distance = GetDistance(ctx, actor);
				if (m_misc.exRender && distance > 500) continue; // Limit render distance

				std::wstring noteName = L"Explorer Note [" + std::to_wstring(static_cast<int>(distance)) + L"m]";
				CG::CoreUObject::FLinearColor color = {1.0f, 0.8f, 0.0f, 1.0f}; // Gold color

				float y = screenPos.Y;
				DrawTextY(ctx.hud, noteName.c_str(), screenPos.X, y, 1, color, ctx.font, m_general.textScale, &y);
			}
		}
	}

	// ===== HELPER METHODS =====

	bool ModuleESP::ShouldRender(const RenderContext& ctx, CG::Engine::AActor* actor) const
	{
		if (!actor) return false;

		float distance = GetDistance(ctx, actor);
		return distance <= m_general.espRange;
	}

	bool ModuleESP::ShouldRenderPlayer(CG::ShooterGame::AShooterCharacter* player) const
	{
		if (!player) return false;

		// Note: We can't access ctx here, so we'll need to pass the playerController
		// For now, we'll assume this check is done in ProcessActors
		return true; // Simplified for now
	}

	bool ModuleESP::ShouldRenderDino(CG::ShooterGame::APrimalDinoCharacter* dino) const
	{
		if (!dino) return false;

		// Basic checks that don't require context
		bool isDead = dino->IsDead();
		bool isWild = !dino->BPIsTamed();
		bool isAlpha = dino->bIsAlpha;

		if (isDead && !m_dino.dead) return false;
		if (isWild && !m_dino.wild) return false;
		if (isAlpha && !m_dino.alpha) return false;

		// Level filter for wild dinos
		if (isWild && m_dino.levelFilter)
		{
			auto statusComp = dino->GetCharacterStatusComponent();
			if (statusComp && statusComp->GetCharacterLevel() < m_dino.minLevel)
				return false;
		}

		// Name filter
		if (m_dino.nameFilter && dino->DescriptiveName.IsValid())
		{
			return PassesNameFilter(dino->DescriptiveName.ToString());
		}

		return true;
	}

	bool ModuleESP::ShouldRenderStructure(CG::Engine::AActor* actor) const
	{
		if (!actor) return false;

		// Basic structure type checks using FNameCache

		// Check specific structure types using FNameCache
		if (m_structure.vault && FNameCache::IsA(actor, FNameCache::Containers::StorageBoxHuge)) return true;
		if (m_structure.dedicatedStorage && FNameCache::IsA(actor, FNameCache::Containers::DedicatedStorage)) return true;
		if (m_structure.storageBoxSmall && FNameCache::IsA(actor, FNameCache::Containers::StorageBoxSmall)) return true;
		if (m_structure.storageBoxLarge && FNameCache::IsA(actor, FNameCache::Containers::StorageBoxLarge)) return true;
		if (m_structure.supplyCrate && FNameCache::IsA(actor, FNameCache::Actors::PrimalStructureItemContainerSupplyCrate)) return true;
		if (m_structure.turret && FNameCache::IsA(actor, FNameCache::Actors::PrimalStructureTurret)) return true;
		if (m_structure.bed && FNameCache::IsA(actor, FNameCache::Actors::PrimalStructureBed)) return true;
		if (m_structure.tekGenerator && FNameCache::IsA(actor, FNameCache::Containers::TekGenerator)) return true;
		if (m_structure.electricGenerator && FNameCache::IsA(actor, FNameCache::Containers::ElectricGenerator)) return true;
		if (m_structure.cryoFridge && FNameCache::IsA(actor, FNameCache::Containers::CryoFridge)) return true;
		if (m_structure.ammoBox && FNameCache::IsA(actor, FNameCache::Containers::AmmoContainer)) return true;

		return false;
	}

	bool ModuleESP::PassesNameFilter(const std::string& dinoName) const
	{
		if (m_dino.nameFilters.empty()) return true;

		std::string lowerName = dinoName;
		std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);

		for (const auto& filter : m_dino.nameFilters)
		{
			if (filter.empty()) continue;

			std::string lowerFilter = filter;
			std::transform(lowerFilter.begin(), lowerFilter.end(), lowerFilter.begin(), ::tolower);

			if (lowerName.find(lowerFilter) != std::string::npos)
				return true;
		}

		return false;
	}

	float ModuleESP::GetDistance(const RenderContext& ctx, CG::Engine::AActor* actor) const
	{
		if (!actor) return FLT_MAX;

		auto actorLoc = actor->K2_GetActorLocation();
		return Vector3(ctx.camLoc.X, ctx.camLoc.Y, ctx.camLoc.Z)
			.Distance(Vector3(actorLoc.X, actorLoc.Y, actorLoc.Z)) / 100.0f;
	}

	void ModuleESP::LoadSettings(nlohmann::json config)
	{
		Module::LoadSettings(config);
		m_friendlyPlayer = GetCheckboxStateById(this->GetName(), 1, config);
		m_enemyPlayer = GetCheckboxStateById(this->GetName(), 2, config);
		m_sleepingPlayer = GetCheckboxStateById(this->GetName(), 3, config);
		m_deadPlayer = GetCheckboxStateById(this->GetName(), 4, config);
		m_extendedPlayerInfo = GetCheckboxStateById(this->GetName(), 5, config);
		m_friendlyDino = GetCheckboxStateById(this->GetName(), 6, config);
		m_enemyDino = GetCheckboxStateById(this->GetName(), 7, config);
		m_deadDino = GetCheckboxStateById(this->GetName(), 8, config);
		m_wildDino = GetCheckboxStateById(this->GetName(), 9, config);
		m_alphaDino = GetCheckboxStateById(this->GetName(), 10, config);
		m_vault = GetCheckboxStateById(this->GetName(), 11, config);
		m_dedicatedStorage = GetCheckboxStateById(this->GetName(), 12, config);
		// m_storageBox = GetCheckboxStateById(this->GetName(), 13);
		m_supplyCrate = GetCheckboxStateById(this->GetName(), 14, config);
		m_item = GetCheckboxStateById(this->GetName(), 15, config);
		m_itemCache = GetCheckboxStateById(this->GetName(), 16, config);
		m_hideHealth = GetCheckboxStateById(this->GetName(), 17, config);
		m_hideTribe = GetCheckboxStateById(this->GetName(), 18, config);
		m_hideAggression = GetCheckboxStateById(this->GetName(), 19, config);

		m_loadoutDummy = GetCheckboxStateById(this->GetName(), 20, config);
		m_storageBoxSmall = GetCheckboxStateById(this->GetName(), 21, config);
		m_storageBoxLarge = GetCheckboxStateById(this->GetName(), 22, config);
		m_turret = GetCheckboxStateById(this->GetName(), 23, config);
		m_bed = GetCheckboxStateById(this->GetName(), 24, config);
		m_tekGenerator = GetCheckboxStateById(this->GetName(), 25, config);
		m_electricGenerator = GetCheckboxStateById(this->GetName(), 26, config);
		m_cryoFridge = GetCheckboxStateById(this->GetName(), 27, config);

		m_hideEmpty = GetCheckboxStateById(this->GetName(), 28, config);

		m_hideEnemyStructures = GetCheckboxStateById(this->GetName(), 29, config);
		m_hideFriendlyStructures = GetCheckboxStateById(this->GetName(), 30, config);
		m_hideTurretSettings = GetCheckboxStateById(this->GetName(), 31, config);

		m_tekSensor = GetCheckboxStateById(this->GetName(), 32, config);

		m_hideStructureHealth = GetCheckboxStateById(this->GetName(), 33, config);
		m_showSpawnpoints = GetCheckboxStateById(this->GetName(), 34, config);
		m_notes = GetCheckboxStateById(this->GetName(), 35, config);
		m_ammoBox = GetCheckboxStateById(this->GetName(), 36, config);
		m_unlocked = GetCheckboxStateById(this->GetName(), 37, config);
		m_exRender = GetCheckboxStateById(this->GetName(), 38, config);
		m_eggs = GetCheckboxStateById(this->GetName(), 39, config);
		m_levelFilter = GetCheckboxStateById(this->GetName(), 40, config);
		m_hideDinoLevel = GetCheckboxStateById(this->GetName(), 41, config);

		m_resourceEsp = GetCheckboxStateById(this->GetName(), 42, config);
		m_metal = GetCheckboxStateById(this->GetName(), 43, config);
		m_oil = GetCheckboxStateById(this->GetName(), 44, config);
		m_crystal = GetCheckboxStateById(this->GetName(), 45, config);
		m_obsidian = GetCheckboxStateById(this->GetName(), 46, config);
		m_silica = GetCheckboxStateById(this->GetName(), 47, config);
		// m_silk = GetCheckboxStateById(this->GetName(), 48, config);

		m_stamina = GetCheckboxStateById(this->GetName(), 48, config);
		m_oxygen = GetCheckboxStateById(this->GetName(), 49, config);
		m_food = GetCheckboxStateById(this->GetName(), 50, config);
		m_weight = GetCheckboxStateById(this->GetName(), 51, config);
		m_meleeDamage = GetCheckboxStateById(this->GetName(), 52, config);
		m_movementSpeed = GetCheckboxStateById(this->GetName(), 53, config);
		m_torpor = GetCheckboxStateById(this->GetName(), 54, config);
		m_dinoNameFilter = GetCheckboxStateById(this->GetName(), 55, config);

		m_structureColor = GetColorValueById(this->GetName(), 1, config);
		m_dinoColor = GetColorValueById(this->GetName(), 2, config);
		m_enemyDinoColor = GetColorValueById(this->GetName(), 3, config);
		m_playerColor = GetColorValueById(this->GetName(), 4, config);
		m_enemyPlayerColor = GetColorValueById(this->GetName(), 5, config);
		m_healthColor = GetColorValueById(this->GetName(), 6, config);
		m_dinoStatsColor = GetColorValueById(this->GetName(), 7, config);

		m_textScale = static_cast<float>(GetSliderCurrentValueById(this->GetName(), 1, config) * 0.1f);
		m_levelSlider = GetSliderCurrentValueById(this->GetName(), 2, config);
		m_resourceRenderDistance = GetSliderCurrentValueById(this->GetName(), 3, config);
		m_resourceRenderReload = GetSliderCurrentValueById(this->GetName(), 4, config);
		m_espRange = static_cast<int>(GetSliderCurrentValueById(this->GetName(), 5, config));

		m_textboxDinoNameFilter = GetTextboxCurrentValueById(this->GetName(), 1, config);
		m_textboxDinoNameFilter2 = GetTextboxCurrentValueById(this->GetName(), 2, config);
		m_textboxDinoNameFilter3 = GetTextboxCurrentValueById(this->GetName(), 3, config);
		m_textboxDinoNameFilter4 = GetTextboxCurrentValueById(this->GetName(), 4, config);
		m_textboxDinoNameFilter5 = GetTextboxCurrentValueById(this->GetName(), 5, config);
		m_textboxDinoNameFilter6 = GetTextboxCurrentValueById(this->GetName(), 6, config);
		m_textboxDinoNameFilter7 = GetTextboxCurrentValueById(this->GetName(), 7, config);
		m_textboxDinoNameFilter8 = GetTextboxCurrentValueById(this->GetName(), 8, config);
	}
}
