#include "pch.h"
#include "ModuleESP.h"

#include <codecvt>

#include "Renderer/Renderer.h"

namespace Base
{
	void ModuleESP::SetupSettings()
	{
		AddSetting(new SliderSetting(XOR("Text Size"), m_textScale, 1.0, 10.0, 1));
		AddSetting(new SliderSetting(XOR("Level Filter (Wild Dinos, Show Nothing Below Level X)"), m_levelSlider, 0.0, 300.0, 2));
		AddSetting(new SliderSetting(XOR("Resource Render Distance (High Distance Can Cause Lag)"), m_resourceRenderDistance, 0.0, 150000.0, 3));
		AddSetting(new SliderSetting(XOR("Resource Render Refresh Rate (Delay Until Reload All In Render => Seconds)"), m_resourceRenderReload, 1.0, 60.0, 4));
		AddSetting(new SliderSetting(XOR("ESP Range"), m_espRange, 0, 1000, 5));

		AddSetting(new CheckboxSetting(XOR("Friendly Players"), m_friendlyPlayer, 1));
		AddSetting(new CheckboxSetting(XOR("Enemy Players"), m_enemyPlayer, 2));
		AddSetting(new CheckboxSetting(XOR("Sleeping Players"), m_sleepingPlayer, 3));
		AddSetting(new CheckboxSetting(XOR("Dead Players"), m_deadPlayer, 4));
		AddSetting(new CheckboxSetting(XOR("Extended Player Information"), m_extendedPlayerInfo, 5));
		AddSetting(new CheckboxSetting(XOR("Friendly Dinos"), m_friendlyDino, 6));
		AddSetting(new CheckboxSetting(XOR("Enemy Dinos"), m_enemyDino, 7));
		AddSetting(new CheckboxSetting(XOR("Dead Dinos"), m_deadDino, 8));
		AddSetting(new CheckboxSetting(XOR("Wild Dinos"), m_wildDino, 9));
		AddSetting(new CheckboxSetting(XOR("Alpha Dinos"), m_alphaDino, 10));
		AddSetting(new CheckboxSetting(XOR("Vaults"), m_vault, 11));
		AddSetting(new CheckboxSetting(XOR("Dedicated Storages"), m_dedicatedStorage, 12));
		// AddSetting(new CheckboxSetting(XOR(L"Storage Boxes")), m_storageBox, 13));
		AddSetting(new CheckboxSetting(XOR("Supply Crates"), m_supplyCrate, 14));
		AddSetting(new CheckboxSetting(XOR("Items"), m_item, 15));
		AddSetting(new CheckboxSetting(XOR("Item Caches"), m_itemCache, 16));
		AddSetting(new CheckboxSetting(XOR("Hide Health"), m_hideHealth, 17));
		AddSetting(new CheckboxSetting(XOR("Hide Tribe"), m_hideTribe, 18));
		AddSetting(new CheckboxSetting(XOR("Hide Dino Aggression"), m_hideAggression, 19));

		AddSetting(new CheckboxSetting(XOR("Loadout Dummy"), m_loadoutDummy, 20));
		AddSetting(new CheckboxSetting(XOR("Small Storage Box"), m_storageBoxSmall, 21));
		AddSetting(new CheckboxSetting(XOR("Large Storage Box"), m_storageBoxLarge, 22));
		AddSetting(new CheckboxSetting(XOR("Turret"), m_turret, 23));
		AddSetting(new CheckboxSetting(XOR("Bed"), m_bed, 24));
		AddSetting(new CheckboxSetting(XOR("Tek Generator"), m_tekGenerator, 25));
		AddSetting(new CheckboxSetting(XOR("Electric Generator"), m_electricGenerator, 26));
		AddSetting(new CheckboxSetting(XOR("Cryo Fridge"), m_cryoFridge, 27));

		AddSetting(new CheckboxSetting(XOR("Hide Empty Containers"), m_hideEmpty, 28));
		AddSetting(new CheckboxSetting(XOR("Hide Enemy Structures"), m_hideEnemyStructures, 29));
		AddSetting(new CheckboxSetting(XOR("Hide Friendly Structures"), m_hideFriendlyStructures, 30));
		AddSetting(new CheckboxSetting(XOR("Hide Turret Settings"), m_hideTurretSettings, 31));

		AddSetting(new CheckboxSetting(XOR("Tek Sensor"), m_tekSensor, 32));
		AddSetting(new CheckboxSetting(XOR("Hide Structure Health"), m_hideStructureHealth, 33));
		AddSetting(new CheckboxSetting(XOR("Player Spawnpoints"), m_showSpawnpoints, 34));
		AddSetting(new CheckboxSetting(XOR("Locked Explorer Notes"), m_notes, 35));
		AddSetting(new CheckboxSetting(XOR("Ammo Box"), m_ammoBox, 36));
		AddSetting(new CheckboxSetting(XOR("Already Unlocked Explorer Notes"), m_unlocked, 37));
		AddSetting(new CheckboxSetting(XOR("Limit Explorer Notes Render Distance"), m_exRender, 38));
		AddSetting(new CheckboxSetting(XOR("Eggs"), m_eggs, 39));
		AddSetting(new CheckboxSetting(XOR("Filter By Level (Wild Dinos)"), m_levelFilter, 40));
		AddSetting(new CheckboxSetting(XOR("Hide Dino Level"), m_hideDinoLevel, 41));

		// Resource ESP
		AddSetting(new CheckboxSetting(XOR("Enable Resource ESP (Causes Lag On Refresh, On High Distance)"), m_resourceEsp, 42));
		AddSetting(new CheckboxSetting(XOR("Show Metal"), m_metal, 43));
		AddSetting(new CheckboxSetting(XOR("Show Oil"), m_oil, 44));
		AddSetting(new CheckboxSetting(XOR("Show Crystal"), m_crystal, 45));
		AddSetting(new CheckboxSetting(XOR("Show Obsidian"), m_obsidian, 46));
		AddSetting(new CheckboxSetting(XOR("Show Silica"), m_silica, 47));
		// AddSetting(new CheckboxSetting(XOR("Show Silk")), m_silk, 48));

		AddSetting(new CheckboxSetting(XOR("Show Dino Stamina (Only Tamed or Knocked Out)"), m_stamina, 48));
		AddSetting(new CheckboxSetting(XOR("Show Dino Oxygen (Only Tamed or Knocked Out)"), m_oxygen, 49));
		AddSetting(new CheckboxSetting(XOR("Show Dino Food (Only Tamed or Knocked Out)"), m_food, 50));
		AddSetting(new CheckboxSetting(XOR("Show Dino Weight (Only Tamed or Knocked Out)"), m_weight, 51));
		AddSetting(new CheckboxSetting(XOR("Show Dino Melee Damage (Only Tamed or Knocked Out)"), m_meleeDamage, 52));
		AddSetting(new CheckboxSetting(XOR("Show Dino Movement Speed (Only Tamed or Knocked Out)"), m_movementSpeed, 53));
		AddSetting(new CheckboxSetting(XOR("Show Dino Torpor"), m_torpor, 54));
		AddSetting(new CheckboxSetting(XOR("Use Dino Name Filter"), m_dinoNameFilter, 55));

		AddSetting(new ColorsSetting(XOR("Structure Color"), XOR("#9900ff"), 1));
		AddSetting(new ColorsSetting(XOR("Friendly Dino Color"), XOR("#0099cc"), 2));
		AddSetting(new ColorsSetting(XOR("Enemy Dino Color"), XOR("#9900cc"), 3));
		AddSetting(new ColorsSetting(XOR("Friendly Player Color"), XOR("#0066ff"), 4));
		AddSetting(new ColorsSetting(XOR("Enemy Player Color"), XOR("#6600ff"), 5));
		AddSetting(new ColorsSetting(XOR("Health Color"), XOR("#ff3300"), 6));
		AddSetting(new ColorsSetting(XOR("Dino Stats Color"), XOR("#006aff"), 7));

		AddSetting(new TextboxSetting(XOR("Dino Name Filter 1 (Enable Below)"), "", 1));
		AddSetting(new TextboxSetting(XOR("Dino Name Filter 2 (Enable Below)"), "", 2));
		AddSetting(new TextboxSetting(XOR("Dino Name Filter 3 (Enable Below)"), "", 3));
		AddSetting(new TextboxSetting(XOR("Dino Name Filter 4 (Enable Below)"), "", 4));
		AddSetting(new TextboxSetting(XOR("Dino Name Filter 5 (Enable Below)"), "", 5));
		AddSetting(new TextboxSetting(XOR("Dino Name Filter 6 (Enable Below)"), "", 6));
		AddSetting(new TextboxSetting(XOR("Dino Name Filter 7 (Enable Below)"), "", 7));
		AddSetting(new TextboxSetting(XOR("Dino Name Filter 8 (Enable Below)"), "", 8));
	}

	const uint8_t LEFT = 0;
	const uint8_t CENTER = 1;
	const uint8_t RIGHT = 2;
	static bool InitializedResourceThread = false;

	struct FOverlappedFoliageElementCached // : public CCG::ShooterGame::FOverlappedFoliageElement
	{
		std::wstring CachedDescriptiveNameW;
		CG::CoreUObject::FVector CachedHarvestLocation;
		// Rest of your structure fields...
	};

	std::string Aggression[] = { XOR("Passive"), XOR("Neutral"), XOR("Aggressive"), XOR("Attack YOUR Target") };
	std::string TurretRange[] = { XOR("Low"), XOR("Medium"), XOR("High") };
	std::string TurretTarget[] = { XOR("All Targets"), XOR("Only Survivors or Tames Creatures"), XOR("Only Survivors"), XOR("Only Wild Creatures"), XOR("Only Tamed Creatures"), XOR("Only Survivors & Mounted Creatures") };

	void ModuleESP::DrawText_(CG::Engine::AHUD* hud, CG::BasicTypes::FString text, float x, float y, uint8_t alignment, CG::CoreUObject::FLinearColor color, CG::Engine::UFont* font, float scale)
	{
		float width;
		float height;

		hud->GetTextSize(text, &width, &height, font, scale);

		switch (alignment)
		{
		case CENTER:
			x -= width / 2;
			break;
		case RIGHT:
			x -= width;
			break;
		}

		CG::CoreUObject::FLinearColor outlineColor = { 0.f, 0.f, 0.f, 1.f };

		// Draw outline
		/*hud->DrawText(text, outlineColor, x - 1.0f, y, font, scale, false);
		hud->DrawText(text, outlineColor, x + 1.0f, y, font, scale, false);
		hud->DrawText(text, outlineColor, x, y - 1.0f, font, scale, false);
		hud->DrawText(text, outlineColor, x, y + 1.0f, font, scale, false);

		hud->DrawText(text, color, x, y, font, scale, false);*/
		hud->Canvas->K2_DrawText(font, text, {x,y}, {scale,scale}, color, 1.f, {0,0,0,0}, {0,0}, 0, 0, 1, {0,0,0,0.5});
		//hud->Canvas->K2_DrawText(font, text, { x, y }, color, 0.f, { 0.f, 0.f, 0.f, 0.f }, { 0.f, 0.f }, false, false, true, { 0.f, 0.f, 0.f, 0.f });
	}

	void ModuleESP::DrawTextY(CG::Engine::AHUD* HUD, CG::BasicTypes::FString text, float x, float y, uint8_t alignment, CG::CoreUObject::FLinearColor color, CG::Engine::UFont* font, float scale, float* yValue)
	{
		float width;
		float height;

		CG::CoreUObject::FVector2D screenPos = { x, y };

		if (Renderer::IsInScreen(screenPos))
		{
			HUD->GetTextSize(text, &width, &height, font, scale);

			if (text.IsValid() && text.ToString().length() != 0)
			{
				DrawText_(HUD, text, x, y, alignment, color, font, scale);
				*yValue += height / 2 + 2;
			}
		}
	}

	void ModuleESP::RenderDino(CG::Engine::UFont* font, CG::ShooterGame::AShooterPlayerController* playerController, CG::Engine::AHUD* hud, CG::ShooterGame::APrimalDinoCharacter* dino)
	{
		auto camLoc = playerController->PlayerCameraManager->GetCameraLocation();
		auto camRot = playerController->PlayerCameraManager->GetCameraRotation();
		auto camFov = playerController->PlayerCameraManager->CameraCachePrivate.POV.FOV;

		CG::CoreUObject::FVector2D screenPos = Renderer::WorldToScreen(dino->K2_GetActorLocation(), camLoc, camRot, camFov);

		if (Renderer::IsInScreen(screenPos))
		{
			std::wstring dinoName = Global::ConvertUtf8ToUtf162(XOR("Unknown"));
			std::wstring tribeName = Global::ConvertUtf8ToUtf162(XOR("No Tribe"));
			std::wstring healthAggression = XOR(L"");
			std::wstring dinoLevel = XOR(L"0");
			std::wstring dinoStats = XOR(L"");

			float distance = Vector3(camLoc.X, camLoc.Y, camLoc.Z).Distance(Vector3(dino->K2_GetActorLocation().X, dino->K2_GetActorLocation().Y, dino->K2_GetActorLocation().Z)) / 100;

			if (dino->DescriptiveName.IsValid())
			{
				if (m_dinoNameFilter)
				{
					std::string descriptiveName = dino->DescriptiveName.ToString();

					// Convert descriptiveName to lowercase for case-insensitive comparison
					std::transform(descriptiveName.begin(), descriptiveName.end(), descriptiveName.begin(), ::tolower);

					// Gather all filter strings into a vector
					std::vector<std::string> filters = {
						m_textboxDinoNameFilter,
						m_textboxDinoNameFilter2,
						m_textboxDinoNameFilter3,
						m_textboxDinoNameFilter4,
						m_textboxDinoNameFilter5,
						m_textboxDinoNameFilter6,
						m_textboxDinoNameFilter7,
						m_textboxDinoNameFilter8
					};

					// Check if all filters are empty
					bool allEmpty = true;
					for (const auto& filter : filters) {
						if (!filter.empty()) {
							allEmpty = false;
							break;
						}
					}

					// If not all filters are empty, check for a match
					if (!allEmpty)
					{
						bool foundMatch = false;
						for (auto filter : filters)
						{
							// Skip empty filters
							if (filter.empty())
								continue;

							// Convert the current filter to lowercase
							std::transform(filter.begin(), filter.end(), filter.begin(), ::tolower);

							// If descriptiveName contains the filter text, mark as found and exit loop
							if (descriptiveName.find(filter) != std::string::npos)
							{
								foundMatch = true;
								break;
							}
						}

						// If none of the filters match, exit the function
						if (!foundMatch) {
							return;
						}
					}
				}

				dinoName = dino->DescriptiveName.ToWString() + XOR(L" [") + std::to_wstring(static_cast<int>(distance)) + XOR(L"m]");

				if (dinoName.find(XOR(L"a ")) == 0)
				{
					dinoName.erase(0, 2);
				}
			}

			if (!m_hideHealth)
			{
				healthAggression = std::to_wstring(static_cast<int>(dino->GetHealth())) + XOR(L"/") + std::to_wstring(static_cast<int>(dino->GetMaxHealth())) + XOR(L" HP ");
			}

			if (!m_hideAggression && dino->BPIsTamed())
			{
				healthAggression += XOR(L"[") + Global::ConvertUtf8ToUtf162(Aggression[dino->TamedAggressionLevel]) + XOR(L"]");
			}

			if (dino->GetCharacterStatusComponent() && (m_stamina || m_oxygen || m_food || m_weight || m_meleeDamage || m_movementSpeed || m_torpor))
			{
				auto statusComponent = dino->GetCharacterStatusComponent();

				if (m_stamina)
				{
					dinoStats += Global::ConvertUtf8ToUtf162(XOR("[Stamina: ")) + std::to_wstring(static_cast<int>(statusComponent->BPGetCurrentStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Stamina))) + XOR(L"/") + std::to_wstring(static_cast<int>(statusComponent->BPGetMaxStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Stamina))) + XOR(L"] ");
				}

				if (m_oxygen)
				{
					dinoStats += Global::ConvertUtf8ToUtf162(XOR("[Oxygen: ")) + std::to_wstring(static_cast<int>(statusComponent->BPGetCurrentStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Oxygen))) + XOR(L"/") + std::to_wstring(static_cast<int>(statusComponent->BPGetMaxStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Oxygen))) + XOR(L"] ");
				}

				if (m_food)
				{
					dinoStats += Global::ConvertUtf8ToUtf162(XOR("[Food: ")) + std::to_wstring(static_cast<int>(statusComponent->BPGetCurrentStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Food))) + XOR(L"/") + std::to_wstring(static_cast<int>(statusComponent->BPGetMaxStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Food))) + XOR(L"] ");
				}

				if (m_weight)
				{
					dinoStats += Global::ConvertUtf8ToUtf162(XOR("[Weight: ")) + std::to_wstring(static_cast<int>(statusComponent->BPGetCurrentStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Weight))) + XOR(L"/") + std::to_wstring(static_cast<int>(statusComponent->BPGetMaxStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Weight))) + XOR(L"] ");
				}

				if (m_meleeDamage)
				{
					dinoStats += Global::ConvertUtf8ToUtf162(XOR("[Melee: ")) + std::to_wstring(static_cast<int>(statusComponent->BPGetCurrentStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::MeleeDamageMultiplier))) + XOR(L"/") + std::to_wstring(static_cast<int>(statusComponent->BPGetMaxStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::MeleeDamageMultiplier))) + XOR(L"] ");
				}

				if (m_movementSpeed)
				{
					dinoStats += Global::ConvertUtf8ToUtf162(XOR("[Speed: ")) + std::to_wstring(static_cast<int>(statusComponent->BPGetCurrentStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::SpeedMultiplier))) + XOR(L"/") + std::to_wstring(static_cast<int>(statusComponent->BPGetMaxStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::SpeedMultiplier))) + XOR(L"] ");
				}

				if (m_torpor)
				{
					dinoStats += Global::ConvertUtf8ToUtf162(XOR("[Torpor: ")) + std::to_wstring(dino->ReplicatedCurrentTorpor) + XOR(L"/") + std::to_wstring(dino->ReplicatedMaxTorpor) + XOR(L"] ");
				}
			}

			if (dino->GetCharacterStatusComponent() && dino->GetCharacterStatusComponent()->GetCharacterLevel() && !m_hideDinoLevel)
			{
				dinoName += Global::ConvertUtf8ToUtf162(XOR(" [Level ")) + std::to_wstring(static_cast<int>(dino->GetCharacterStatusComponent()->GetCharacterLevel())) + XOR(L"]");
			}

			if (dino->TribeName.IsValid())
			{
				tribeName = dino->TribeName.ToWString();
			}

			float healthPercent = dino->GetHealth() / dino->GetMaxHealth() * 100;
			CG::CoreUObject::FLinearColor colorHealth = m_healthColor;
			if (healthPercent <= 25) colorHealth = { m_healthColor.R, m_healthColor.G, m_healthColor.B, 0.8f };
			else if (healthPercent <= 50) colorHealth = { m_healthColor.R, m_healthColor.G, m_healthColor.B, 0.6f };

			float y = screenPos.Y;

			if (!m_hideHealth || !m_hideAggression)
			{
				DrawTextY(hud, healthAggression.c_str(), screenPos.X, y, 1, { colorHealth.R, colorHealth.G, colorHealth.B, 1.f }, font, m_textScale, &y);
			}

			if ((m_stamina || m_oxygen || m_food || m_weight || m_meleeDamage || m_movementSpeed || m_torpor))
			{
				DrawTextY(hud, dinoStats.c_str(), screenPos.X, y, 1, { m_dinoStatsColor.R, m_dinoStatsColor.G, m_dinoStatsColor.B, 1.f }, font, m_textScale, &y);
			}

			CG::CoreUObject::FLinearColor m_newColor = m_dinoColor;

			if (dino->TargetingTeam != playerController->TargetingTeam)
			{
				m_newColor = m_enemyDinoColor;
			}

			DrawTextY(hud, dinoName.c_str(), screenPos.X, y, 1, m_newColor, font, m_textScale, &y);

			if (!m_hideTribe && dino->BPIsTamed())
			{
				DrawTextY(hud, tribeName.c_str(), screenPos.X, y, 1, m_newColor, font, m_textScale, &y);

				if (dino->UploadedFromServerName.IsValid())
				{
					std::wstring tamedServer = dino->UploadedFromServerName.ToWString();
					std::erase(tamedServer, '\n');
					DrawTextY(hud, tamedServer.c_str(), screenPos.X, y, 1, m_newColor, font, m_textScale, &y);
				}
			}
		}
	}

	void ModuleESP::RenderPlayer(CG::Engine::UFont* font, CG::ShooterGame::AShooterPlayerController* playerController, CG::Engine::AHUD* hud, CG::ShooterGame::AShooterCharacter* player)
	{
		auto camLoc = playerController->PlayerCameraManager->GetCameraLocation();
		auto camRot = playerController->PlayerCameraManager->GetCameraRotation();
		auto camFov = playerController->PlayerCameraManager->CameraCachePrivate.POV.FOV;

		std::wstring playerName = Global::ConvertUtf8ToUtf162(XOR("Unknown"));
		std::wstring implantId = XOR(L"-");
		std::wstring platformName = XOR(L"-");
		std::wstring platformId = XOR(L"-");
		std::wstring tribeName = Global::ConvertUtf8ToUtf162(XOR("No Tribe"));


		int distance = static_cast<int>(Vector3(camLoc.X, camLoc.Y, camLoc.Z).Distance(
			Vector3(player->K2_GetActorLocation().X, player->K2_GetActorLocation().Y, player->K2_GetActorLocation().Z)) / 100);
		bool isAllied = player->TargetingTeam == playerController->TargetingTeam;

		CG::CoreUObject::FLinearColor color = m_playerColor;

		if (!isAllied)
		{
			color = m_enemyPlayerColor;
		}

		auto victoryCore = reinterpret_cast<CG::ShooterGame::UVictoryCore*>(playerController);
		/*CG::FColor teamColor = victoryCore->STATIC_GetTeamColor(player->TargetingTeam);

		color.R = teamColor.R;
		color.G = teamColor.G;
		color.B = teamColor.B;*/

		// if (isAllied)
		// {
		// 	color = { 0.00f, 0.95f, 0.92f, 1.f };
		// }

		if (player->IsDead())
		{
			color = { 0.3f, 0.3f, 0.3f, 1.f };
		}

		if (player->PlayerName.IsValid())
		{
			playerName = player->PlayerName.ToWString();
		}

		if (player->LinkedPlayerIDString().IsValid())
		{
			implantId = player->LinkedPlayerIDString().ToWString();
		}

		if (player->PlatformProfileName.IsValid())
		{
			platformName = player->PlatformProfileName.ToWString();
		}

		/*if (player->VivoxUsername.IsValid())
		{
			platformId = player->VivoxUsername.ToStringW();
		}*/

		if (player->TribeName.IsValid())
		{
			tribeName = player->TribeName.ToWString();
		}

		CG::CoreUObject::FVector2D screenPos = Renderer::WorldToScreen(player->K2_GetActorLocation(), camLoc, camRot, camFov);

		if (Global::IsInScreen(screenPos, Global::ScreenWidth, Global::ScreenHeight))
		{
			float healthPercent = player->GetHealth() / player->GetMaxHealth() * 100;
			CG::CoreUObject::FLinearColor colorHealth = m_healthColor;
			if (healthPercent <= 25) colorHealth = { m_healthColor.R, m_healthColor.G, m_healthColor.B, 0.8f };
			else if (healthPercent <= 50) colorHealth = { m_healthColor.R, m_healthColor.G, m_healthColor.B, 0.6f };

			float y = screenPos.Y;

			DrawTextY(hud, (playerName + XOR(L" - ") + XOR(L"Lvl ") + std::to_wstring(player->GetCharacterStatusComponent()->GetCharacterLevel()) + XOR(L" [") + std::to_wstring(distance) + XOR(L"m]")).c_str(), screenPos.X, y, 1, color, font, m_textScale + 0.10f, &y);

			if (!isAllied)
			{
				if (!m_hideTribe)
				{
					DrawTextY(hud, tribeName.c_str(), screenPos.X, y, 1, m_playerColor, font, m_textScale + 0.10f, &y);
				}

				if (m_extendedPlayerInfo)
				{
					DrawTextY(hud, platformName.c_str(), screenPos.X, y, 1, m_playerColor, font, m_textScale + 0.10f, &y);
					DrawTextY(hud, implantId.c_str(), screenPos.X, y, 1, m_playerColor, font, m_textScale + 0.10f, &y);
					DrawTextY(hud, platformId.c_str(), screenPos.X, y, 1, m_playerColor, font, m_textScale + 0.10f, &y);
				}
			}

			if (!m_hideHealth)
			{
				DrawTextY(hud, (std::to_wstring(static_cast<int>(player->GetHealth())) + XOR(L"/") + std::to_wstring(static_cast<int>(player->GetMaxHealth())) + XOR(L" HP")).c_str(), screenPos.X, y, 1, { colorHealth.R, colorHealth.G, colorHealth.B, 1.f }, font, m_textScale + 0.10f, &y);
			}
		}
	}

	void ModuleESP::OnPostRender(CG::ShooterGame::UShooterGameViewportClient* viewport, CG::Engine::UCanvas* canvas, CG::Engine::UWorld* world, CG::Engine::UFont* font)
	{
		CG::ShooterGame::AShooterPlayerController* playerController = Global::GetPlayerController(world);
		if (playerController == nullptr || playerController->PlayerCameraManager == nullptr || !playerController->GetPlayerCharacter()) return;
		CG::ShooterGame::AShooterCharacter* selfPlayer = playerController->GetPlayerCharacter();
		if (selfPlayer == nullptr) return;
		CG::Engine::ULevel* persistentLevel = world->PersistentLevel;
		if (persistentLevel == nullptr || !persistentLevel->ActorsList.Count()) return;
		CG::BasicTypes::TArray<CG::Engine::AActor*> actorArray = persistentLevel->ActorsList;

		CG::Engine::AHUD* hud = playerController->GetShooterHud();
		if (!hud) return;

		hud->Canvas = canvas;

		if (m_resourceEsp)
		{
			auto camLoc = playerController->PlayerCameraManager->GetCameraLocation();
			auto camRot = playerController->PlayerCameraManager->GetCameraRotation();
			auto camFov = playerController->PlayerCameraManager->CameraCachePrivate.POV.FOV;
			CG::CoreUObject::FLinearColor color = { 0.88f, 0.88f, 0.44f, 1.f };

			static std::vector<FOverlappedFoliageElementCached> cachedFoliage;
			static std::chrono::steady_clock::time_point lastExecutionTime = std::chrono::steady_clock::now();
			auto currentTime = std::chrono::steady_clock::now();
			auto elapsedTime = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - lastExecutionTime).count();

			if (elapsedTime > (m_resourceRenderReload * 1000))
			{
				auto victoryCore = Global::GetVictoryCore();
				CG::CoreUObject::FVector origin = selfPlayer->K2_GetActorLocation();
				CG::BasicTypes::TArray<CG::ShooterGame::FOverlappedFoliageElement> foliage;
				victoryCore->STATIC_ServerSearchFoliage(world, origin, static_cast<float>(m_resourceRenderDistance), &foliage, true, true, true, false, false);

				lastExecutionTime = currentTime;
				cachedFoliage.clear();

				for (int i = 0; i < foliage.Count(); i++)
				{
					std::wstring descName = foliage[i].HarvestingComponent->DescriptiveName.ToWString();

					if (descName.find(L"Bush") != std::wstring::npos) continue;
					if (descName.find(L"Rock") != std::wstring::npos) continue;
					if (descName.find(L"Stone") != std::wstring::npos) continue;
					if (descName.find(L"Tree") != std::wstring::npos) continue;
					if (descName.find(L"Oil") != std::wstring::npos && !m_oil) continue;
					if (descName.find(L"Crystal") != std::wstring::npos && !m_crystal) continue;
					if (descName.find(L"Metal") != std::wstring::npos && !m_metal) continue;
					if (descName.find(L"Obsidian") != std::wstring::npos && !m_obsidian) continue;
					if (descName.find(L"Silica") != std::wstring::npos && !m_silica) continue;

					FOverlappedFoliageElementCached cachedElement;
					cachedElement.CachedDescriptiveNameW = descName;
					cachedElement.CachedHarvestLocation = foliage[i].HarvestLocation;

					cachedFoliage.push_back(cachedElement);
				}
			}

			// Now use the cached foliage
			{
				for (int i = 0; i < cachedFoliage.size(); i++)
				{
					if (cachedFoliage[i].CachedDescriptiveNameW.empty()) continue;
					std::wstring descName = cachedFoliage[i].CachedDescriptiveNameW;

					if (descName.find(L"Oil") != std::wstring::npos && m_oil ||
						descName.find(L"Crystal") != std::wstring::npos && m_crystal ||
						descName.find(L"Metal") != std::wstring::npos && m_metal ||
						descName.find(L"Obsidian") != std::wstring::npos && m_obsidian ||
						descName.find(L"Silica") != std::wstring::npos && m_silica)
					{
						float distance = Vector3(camLoc.X, camLoc.Y, camLoc.Z).Distance(Vector3(cachedFoliage[i].CachedHarvestLocation.X, cachedFoliage[i].CachedHarvestLocation.Y, cachedFoliage[i].CachedHarvestLocation.Z)) / 100;

						//if (distance > 500) continue;

						std::wstring resourceLabel = cachedFoliage[i].CachedDescriptiveNameW + XOR(L" [") + std::to_wstring(static_cast<int>(distance)) + XOR(L"m]");
						CG::CoreUObject::FVector2D screenPos = Renderer::WorldToScreen(cachedFoliage[i].CachedHarvestLocation, camLoc, camRot, camFov);

						float yPosFoliage = screenPos.Y;

						DrawTextY(hud, resourceLabel.c_str(), screenPos.X, yPosFoliage, 1, { color.R, color.G, color.B, 1.f }, font, m_textScale, &yPosFoliage);
					}
				}
			}
		}

		if (m_notes)
		{
			bool foundCorrectLevel = false;

			for (int i = 0; i < world->Levels.Count(); i++)
			{
				for (int y = 0; y < world->Levels[i]->ActorsList.Count(); y++)
				{
					auto currentActor = world->Levels[i]->ActorsList[y];
					{
						if (currentActor == nullptr) continue;
						if (currentActor->RootComponent == nullptr) continue;
					}

					CG::CoreUObject::FLinearColor color = { 0.66f, 0.99f, 0.66f, 1.f };

					auto camLoc = playerController->PlayerCameraManager->GetCameraLocation();
					auto camRot = playerController->PlayerCameraManager->GetCameraRotation();
					auto camFov = playerController->PlayerCameraManager->CameraCachePrivate.POV.FOV;

					if (FNameCache::IsA(currentActor, FNameCache::Actors::ExplorerChestBase))
					{
						auto explorerNote = static_cast<CG::ExplorerChest_Base::AExplorerChest_Base_C*>(currentActor);
						foundCorrectLevel = true;

						if (explorerNote->bIsUnlocked && !m_unlocked) continue;

						int distance = static_cast<int>(Vector3(camLoc.X, camLoc.Y, camLoc.Z).Distance(
							Vector3(currentActor->K2_GetActorLocation().X, currentActor->K2_GetActorLocation().Y, currentActor->K2_GetActorLocation().Z)) / 100);

						if (m_exRender && distance > 3000) continue;

						std::wstringstream ss;
						ss << explorerNote->GetName().c_str() << XOR(L" [") << distance << XOR(L"m]");
						std::wstring text = ss.str();

						CG::CoreUObject::FVector2D screenPos = Renderer::WorldToScreen(currentActor->K2_GetActorLocation(), camLoc, camRot, camFov);
						float yPosExplorerNote = screenPos.Y;
						DrawTextY(hud, text.c_str(), screenPos.X, yPosExplorerNote, 1, { color.R, color.G, color.B, 1.f }, font, m_textScale, &yPosExplorerNote);
					}
				}

				if (foundCorrectLevel) break;
			}
		}

		if (m_showSpawnpoints)
		{
			auto victoryCore = reinterpret_cast<CG::ShooterGame::UVictoryCore*>(playerController);
			auto playStarts = victoryCore->STATIC_GetAllPlayerStarts(world);

			for (int i = 0; i < playStarts.Count(); i++)
			{
				auto currentActor = playStarts[i];
				{
					if (currentActor == nullptr) continue;
					if (currentActor->RootComponent == nullptr) continue;
				}

				CG::CoreUObject::FLinearColor color = { 0.99f, 0.99f, 0.99f, 1.f };
				auto camLoc = playerController->PlayerCameraManager->GetCameraLocation();
				auto camRot = playerController->PlayerCameraManager->GetCameraRotation();
				auto camFov = playerController->PlayerCameraManager->CameraCachePrivate.POV.FOV;

				int distance = static_cast<int>(Vector3(camLoc.X, camLoc.Y, camLoc.Z).Distance(
					Vector3(currentActor->K2_GetActorLocation().X, currentActor->K2_GetActorLocation().Y, currentActor->K2_GetActorLocation().Z)) / 100);

				std::wstringstream ss;
				ss << Global::ConvertUtf8ToUtf162(XOR("Spawn Point")) << XOR(L" [") << distance << XOR(L"m]");
				std::wstring text = ss.str();

				CG::CoreUObject::FVector2D screenPos = Renderer::WorldToScreen(currentActor->K2_GetActorLocation(), camLoc, camRot, camFov);
				float yPosSpawnPoints = screenPos.Y;
				DrawTextY(hud, text.c_str(), screenPos.X, screenPos.Y, 1, { color.R, color.G, color.B, 1.f }, font, m_textScale, &yPosSpawnPoints);
			}
		}

		// Use FNameCache for efficient structure filtering - no more static declarations needed!

		for (int i = 0; i < actorArray.Count(); i++)
		{
			auto currentActor = actorArray[i];
			{
				if (currentActor == nullptr) continue;
				if (currentActor->RootComponent == nullptr) continue;

				if (FNameCache::IsAnyStructure(currentActor->Name.ComparisonIndex))
				{
					continue;
				}
			}

			if (FNameCache::IsA(currentActor, FNameCache::Actors::ShooterCharacter))
			{
				auto player = static_cast<CG::ShooterGame::AShooterCharacter*>(currentActor);

				if (player->LinkedPlayerDataID == selfPlayer->LinkedPlayerDataID || !player->PlayerName.IsValid()) continue;

				if (m_friendlyPlayer && player->TargetingTeam == selfPlayer->TargetingTeam && !player->bIsSleeping && player->IsAlive())
				{
					RenderPlayer(font, playerController, hud, player);
				}

				if (m_sleepingPlayer && player->bIsSleeping)
				{
					RenderPlayer(font, playerController, hud, player);
				}

				if (m_enemyPlayer && player->TargetingTeam != selfPlayer->TargetingTeam && !player->bIsSleeping && player->IsAlive())
				{
					RenderPlayer(font, playerController, hud, player);
				}

				if (m_deadPlayer && !player->IsAlive())
				{
					RenderPlayer(font, playerController, hud, player);
				}
			}

			if (FNameCache::IsA(currentActor, FNameCache::Actors::PrimalDinoCharacter))
			{
				auto dino = static_cast<CG::ShooterGame::APrimalDinoCharacter*>(currentActor);

				if (dino == playerController->AcknowledgedPawn) continue;

				if (m_friendlyDino && dino->TargetingTeam == selfPlayer->TargetingTeam && dino->BPIsTamed() && dino->ReplicatedCurrentHealth > 1.f)
				{
					RenderDino(font, playerController, hud, dino);
				}

				if (m_enemyDino && dino->TargetingTeam != selfPlayer->TargetingTeam && dino->BPIsTamed() && dino->ReplicatedCurrentHealth > 1.f)
				{
					RenderDino(font, playerController, hud, dino);
				}

				if (m_deadDino && dino->ReplicatedCurrentHealth <= 1.f)
				{
					RenderDino(font, playerController, hud, dino);
				}

				if (m_wildDino && !dino->BPIsTamed() && dino->ReplicatedCurrentHealth > 1.f)
				{
					if ((dino->GetCharacterStatusComponent() && dino->GetCharacterStatusComponent()->GetCharacterLevel() >= m_levelSlider) || !m_levelFilter)
					{
						RenderDino(font, playerController, hud, dino);
					}
				}

				if (m_alphaDino && !dino->BPIsTamed() && std::wcsstr(dino->DescriptiveName.wc_str(), L"Alpha") != nullptr)
				{
					RenderDino(font, playerController, hud, dino);
				}
			}

			CG::CoreUObject::FLinearColor color = m_structureColor;

			auto camLoc = playerController->PlayerCameraManager->GetCameraLocation();
			auto camRot = playerController->PlayerCameraManager->GetCameraRotation();
			auto camFov = playerController->PlayerCameraManager->CameraCachePrivate.POV.FOV;

			if (FNameCache::IsA(currentActor, FNameCache::Actors::PrimalStructure))
			{
				if (m_hideFriendlyStructures && currentActor->TargetingTeam == selfPlayer->TargetingTeam) continue;
				if (m_hideEnemyStructures && currentActor->TargetingTeam != selfPlayer->TargetingTeam) continue;

				int currentDistance = static_cast<int>(Vector3(camLoc.X, camLoc.Y, camLoc.Z).Distance(Vector3(currentActor->K2_GetActorLocation().X, currentActor->K2_GetActorLocation().Y, currentActor->K2_GetActorLocation().Z)) / 100);

				if (m_espRange < currentDistance) continue;

				// Vault
				/*if (m_vault && currentActor->IsA(CG::AStorageBox_Huge_C::StaticClass()))
				{
					auto vault = static_cast<CG::AStorageBox_Huge_C*>(currentActor);

					int itemCount = vault->CurrentItemCount;
					int maxItemCount = vault->MaxItemCount;

					if (m_hideEmpty && itemCount <= 0) continue;

					CG::CoreUObject::FVector2D screenPos = Renderer::WorldToScreen(currentActor->K2_GetActorLocation(), camLoc, camRot, camFov);
					float yPos = screenPos.Y;

					int distance = static_cast<int>(Vector3(camLoc.X, camLoc.Y, camLoc.Z).Distance(
						Vector3(selfPlayer->K2_GetActorLocation().X, selfPlayer->K2_GetActorLocation().Y, selfPlayer->K2_GetActorLocation().Z)) / 100);
					std::wstringstream ss;
					ss << vault->DescriptiveName.ToStringW() << XOR(L" [") << distance << XOR(L"m]");
					std::wstring text = ss.str();

					DrawTextY(hud, text.c_str(), screenPos.X, yPos, 1, { color.R, color.G, color.B, 1.f }, font, m_textScale, &yPos);

					if (!m_hideItemAmount)
					{
						DrawTextY(hud, std::to_wstring(itemCount).append(XOR(L"/")).append(std::to_wstring(maxItemCount)).c_str(), screenPos.X, yPos, 1, color, font, m_textScale, &yPos);
					}

					if (!m_hideStructureHealth)
					{
						int health = static_cast<int>(vault->Health);
						int maxHealth = static_cast<int>(vault->GetMaxHealth());

						DrawTextY(hud, std::to_wstring(health).append(XOR(L"/")).append(std::to_wstring(maxHealth)).c_str(), screenPos.X, yPos, 1, color, font, m_textScale, &yPos);
					}
				}*/

				// Turrets
				if (m_turret && FNameCache::IsA(currentActor, FNameCache::Actors::PrimalStructureTurret))
				{
					std::string TurretRange[] = { XOR("Low"), XOR("Medium"), XOR("High") };
					std::string TurretTarget[] = { XOR("All Targets"), XOR("Only Survivors or Tames Creatures"), XOR("Only Survivors"), XOR("Only Wild Creatures"), XOR("Only Tamed Creatures"), XOR("Only Survivors & Mounted Creatures") };

					auto turret = static_cast<CG::ShooterGame::APrimalStructureTurret*>(currentActor);

					int itemCount = turret->CurrentItemCount;
					int maxItemCount = turret->MaxItemCount;
					int bullets = turret->NumBullets;
					bool battery = turret->bPoweredHasBattery;

					std::wstring turretRange = Global::ConvertUtf8ToUtf162(TurretRange[turret->RangeSetting]);
					std::wstring turretTarget = Global::ConvertUtf8ToUtf162(TurretTarget[turret->AISetting]);

					if (m_hideEmpty && itemCount <= 0) continue;

					CG::CoreUObject::FVector2D screenPos = Renderer::WorldToScreen(currentActor->K2_GetActorLocation(), camLoc, camRot, camFov);
					float yPosTurrets = screenPos.Y;

					DrawTextY(hud, turret->DescriptiveName.ToWString().c_str(), screenPos.X, yPosTurrets, 1, { color.R, color.G, color.B, 1.f }, font, m_textScale, &yPosTurrets);

					std::wstring bulletsText = XOR(L"[") + std::to_wstring(bullets) + XOR(L"]");

					if (!m_hideTurretSettings)
					{
						std::wostringstream batteryStream;
						batteryStream << Global::ConvertUtf8ToUtf162(XOR("[Battery: ")) << (battery ? Global::ConvertUtf8ToUtf162(XOR("YES")) : Global::ConvertUtf8ToUtf162(XOR("NO"))) << XOR(L"]");
						std::wstring batteryText = batteryStream.str();

						DrawTextY(hud, (bulletsText + L" " + batteryText).c_str(), screenPos.X, yPosTurrets, 1, color, font, m_textScale, &yPosTurrets);
						DrawTextY(hud, (Global::ConvertUtf8ToUtf162(XOR("[Range: ")) + turretRange + XOR(L"]")).c_str(), screenPos.X, yPosTurrets, 1, color, font, m_textScale, &yPosTurrets);
						DrawTextY(hud, (Global::ConvertUtf8ToUtf162(XOR("[Target: ")) + turretTarget + XOR(L"]")).c_str(), screenPos.X, yPosTurrets, 1, color, font, m_textScale, &yPosTurrets);
					}

					if (!m_hideItemAmount)
					{
						DrawTextY(hud, std::to_wstring(itemCount).append(XOR(L"/")).append(std::to_wstring(maxItemCount)).c_str(), screenPos.X, yPosTurrets, 1, color, font, m_textScale, &yPosTurrets);
					}

					if (!m_hideStructureHealth)
					{
						int health = static_cast<int>(turret->Health);
						int maxHealth = static_cast<int>(turret->GetMaxHealth());

						DrawTextY(hud, std::to_wstring(health).append(XOR(L"/")).append(std::to_wstring(maxHealth)).c_str(), screenPos.X, yPosTurrets, 1, color, font, m_textScale, &yPosTurrets);
					}
				}

				// Bed
				if (m_bed && FNameCache::IsA(currentActor, FNameCache::Actors::PrimalStructureBed))
				{
					auto bed = static_cast<CG::ShooterGame::APrimalStructureBed*>(currentActor);

					CG::CoreUObject::FVector2D screenPos = Renderer::WorldToScreen(currentActor->K2_GetActorLocation(), camLoc, camRot, camFov);
					float yPos = screenPos.Y;
					DrawTextY(hud, bed->DescriptiveName.ToWString().c_str(), screenPos.X, yPos, 1, { color.R, color.G, color.B, 1.f }, font, m_textScale, &yPos);
				}

				// Tek Generator
				if (m_tekGenerator && currentActor->IsA(CG::ShooterGame::APrimalStructure::StaticClass()) && currentActor->GetFullName().find("TekGenerator") != std::string::npos)
				{
					auto tekGenerator = static_cast<CG::StorageBox_TekGenerator::AStorageBox_TekGenerator_C*>(currentActor);

					int itemCount = tekGenerator->CurrentItemCount;
					int maxItemCount = tekGenerator->MaxItemCount;

					if (m_hideEmpty && itemCount <= 0) continue;

					CG::CoreUObject::FVector2D screenPos = Renderer::WorldToScreen(currentActor->K2_GetActorLocation(), camLoc, camRot, camFov);
					float yPos = screenPos.Y;
					DrawTextY(hud, tekGenerator->DescriptiveName.ToWString().c_str(), screenPos.X, yPos, 1, { color.R, color.G, color.B, 1.f }, font, m_textScale, &yPos);

					if (!m_hideItemAmount)
					{
						DrawTextY(hud, std::to_wstring(itemCount).append(XOR(L"/")).append(std::to_wstring(maxItemCount)).c_str(), screenPos.X, yPos, 1, color, font, m_textScale, &yPos);
					}

					if (!m_hideStructureHealth)
					{
						int health = static_cast<int>(tekGenerator->Health);
						int maxHealth = static_cast<int>(tekGenerator->GetMaxHealth());

						DrawTextY(hud, std::to_wstring(health).append(XOR(L"/")).append(std::to_wstring(maxHealth)).c_str(), screenPos.X, yPos, 1, color, font, m_textScale, &yPos);
					}
				}

				// Ammo Box
				/*if (m_ammoBox && currentActor->IsA(CG::AStructureAmmoContainer_C::StaticClass()))
				{
					auto ammoContainer = static_cast<CG::AStructureAmmoContainer_C*>(currentActor);

					int itemCount = ammoContainer->CurrentItemCount;
					int maxItemCount = ammoContainer->MaxItemCount;

					if (m_hideEmpty && itemCount <= 0) continue;

					CG::CoreUObject::FVector2D screenPos = Renderer::WorldToScreen(currentActor->K2_GetActorLocation(), camLoc, camRot, camFov);
					float yPos = screenPos.Y;
					DrawTextY(hud, ammoContainer->DescriptiveName.ToStringW().c_str(), screenPos.X, yPos, 1, { color.R, color.G, color.B, 1.f }, font, m_textScale, &yPos);

					if (!m_hideItemAmount)
					{
						DrawTextY(hud, std::to_wstring(itemCount).append(XOR(L"/")).append(std::to_wstring(maxItemCount)).c_str(), screenPos.X, yPos, 1, color, font, m_textScale, &yPos);
					}

					if (!m_hideStructureHealth)
					{
						int health = static_cast<int>(ammoContainer->Health);
						int maxHealth = static_cast<int>(ammoContainer->GetMaxHealth());

						DrawTextY(hud, std::to_wstring(health).append(XOR(L"/")).append(std::to_wstring(maxHealth)).c_str(), screenPos.X, yPos, 1, color, font, m_textScale, &yPos);
					}
				}*/

				// Electric Generator
				if (m_electricGenerator && FNameCache::IsA(currentActor, FNameCache::Containers::ElectricGenerator))
				{
					auto electricGenerator = static_cast<CG::StorageBox_TekGenerator::AStorageBox_TekGenerator_C*>(currentActor);

					int itemCount = electricGenerator->CurrentItemCount;
					int maxItemCount = electricGenerator->MaxItemCount;

					if (m_hideEmpty && itemCount <= 0) continue;

					CG::CoreUObject::FVector2D screenPos = Renderer::WorldToScreen(currentActor->K2_GetActorLocation(), camLoc, camRot, camFov);
					float yPos = screenPos.Y;
					DrawTextY(hud, electricGenerator->DescriptiveName.ToWString().c_str(), screenPos.X, yPos, 1, { color.R, color.G, color.B, 1.f }, font, m_textScale, &yPos);

					if (!m_hideItemAmount)
					{
						DrawTextY(hud, std::to_wstring(itemCount).append(XOR(L"/")).append(std::to_wstring(maxItemCount)).c_str(), screenPos.X, yPos, 1, color, font, m_textScale, &yPos);
					}

					if (!m_hideStructureHealth)
					{
						int health = static_cast<int>(electricGenerator->Health);
						int maxHealth = static_cast<int>(electricGenerator->GetMaxHealth());

						DrawTextY(hud, std::to_wstring(health).append(XOR(L"/")).append(std::to_wstring(maxHealth)).c_str(), screenPos.X, yPos, 1, color, font, m_textScale, &yPos);
					}
				}

				// Dedicated Storage
				if (m_dedicatedStorage && FNameCache::IsA(currentActor, FNameCache::Containers::DedicatedStorage))
				{
					auto dedicatedStorage = static_cast<CG::BP_DedicatedStorage::ABP_DedicatedStorage_C*>(currentActor);

					int itemCount = dedicatedStorage->CurrentItemCount;
					int maxItemCount = dedicatedStorage->MaxItemCount;

					if (m_hideEmpty && itemCount <= 0) continue;

					CG::CoreUObject::FVector2D screenPos = Renderer::WorldToScreen(currentActor->K2_GetActorLocation(), camLoc, camRot, camFov);
					float yPos = screenPos.Y;
					DrawTextY(hud, dedicatedStorage->DescriptiveName.ToWString().c_str(), screenPos.X, yPos, 1, { color.R, color.G, color.B, 1.f }, font, m_textScale, &yPos);

					if (!m_hideItemAmount)
					{
						DrawTextY(hud, std::to_wstring(itemCount).append(XOR(L"/")).append(std::to_wstring(maxItemCount)).c_str(), screenPos.X, yPos, 1, color, font, m_textScale, &yPos);
					}

					if (!m_hideStructureHealth)
					{
						int health = static_cast<int>(dedicatedStorage->Health);
						int maxHealth = static_cast<int>(dedicatedStorage->GetMaxHealth());

						DrawTextY(hud, std::to_wstring(health).append(XOR(L"/")).append(std::to_wstring(maxHealth)).c_str(), screenPos.X, yPos, 1, color, font, m_textScale, &yPos);
					}
				}

				// Drops
				if (m_supplyCrate && FNameCache::IsA(currentActor, FNameCache::Actors::PrimalStructureItemContainerSupplyCrate))
				{
					auto supplyCrate = static_cast<CG::ShooterGame::APrimalStructureItemContainer_SupplyCrate*>(currentActor);

					float distance = Vector3(camLoc.X, camLoc.Y, camLoc.Z).Distance(Vector3(supplyCrate->K2_GetActorLocation().X, supplyCrate->K2_GetActorLocation().Y, supplyCrate->K2_GetActorLocation().Z)) / 100;

					std::wstring supplyName = supplyCrate->DescriptiveName.ToWString() + XOR(L" [") + std::to_wstring(static_cast<int>(distance)) + XOR(L"m]");

					CG::CoreUObject::FVector2D screenPos = Renderer::WorldToScreen(currentActor->K2_GetActorLocation(), camLoc, camRot, camFov);
					float yPos = screenPos.Y;
					DrawTextY(hud, supplyName.c_str(), screenPos.X, screenPos.Y, 1, { color.R, color.G, color.B, 1.f }, font, m_textScale, &yPos);
				}

				// Small Storage Box
				/*if (m_storageBoxSmall && currentActor->IsA(CG::AStorageBox_Small_C::StaticClass()))
				{
					auto smallStorageBox = static_cast<CG::AStorageBox_Small_C*>(currentActor);

					int itemCount = smallStorageBox->CurrentItemCount;
					int maxItemCount = smallStorageBox->MaxItemCount;

					if (m_hideEmpty && itemCount <= 0) continue;

					CG::CoreUObject::FVector2D screenPos = Renderer::WorldToScreen(currentActor->K2_GetActorLocation(), camLoc, camRot, camFov);
					float yPos = screenPos.Y;
					DrawTextY(hud, smallStorageBox->DescriptiveName.ToStringW().c_str(), screenPos.X, screenPos.Y, 1, { color.R, color.G, color.B, 1.f }, font, m_textScale, &yPos);

					if (!m_hideItemAmount)
					{
						DrawTextY(hud, std::to_wstring(itemCount).append(XOR(L"/")).append(std::to_wstring(maxItemCount)).c_str(), screenPos.X, screenPos.Y, 1, color, font, m_textScale, &yPos);
					}

					if (!m_hideStructureHealth)
					{
						int health = static_cast<int>(smallStorageBox->Health);
						int maxHealth = static_cast<int>(smallStorageBox->GetMaxHealth());

						DrawTextY(hud, std::to_wstring(health).append(XOR(L"/")).append(std::to_wstring(maxHealth)).c_str(), screenPos.X, screenPos.Y, 1, color, font, m_textScale, &yPos);
					}
				}

				// Large Storage Box
				if (m_storageBoxLarge && currentActor->IsA(CG::AStorageBox_Large_C::StaticClass()))
				{
					auto largeStorageBox = static_cast<CG::AStorageBox_Large_C*>(currentActor);

					int itemCount = largeStorageBox->CurrentItemCount;
					int maxItemCount = largeStorageBox->MaxItemCount;

					if (m_hideEmpty && itemCount <= 0) continue;

					CG::CoreUObject::FVector2D screenPos = Renderer::WorldToScreen(currentActor->K2_GetActorLocation(), camLoc, camRot, camFov);
					float yPos = screenPos.Y;
					DrawTextY(hud, largeStorageBox->DescriptiveName.ToStringW().c_str(), screenPos.X, screenPos.Y, 1, { color.R, color.G, color.B, 1.f }, font, m_textScale, &yPos);

					if (!m_hideItemAmount)
					{
						DrawTextY(hud, std::to_wstring(itemCount).append(XOR(L"/")).append(std::to_wstring(maxItemCount)).c_str(), screenPos.X, screenPos.Y, 1, color, font, m_textScale, &yPos);
					}

					if (!m_hideStructureHealth)
					{
						int health = static_cast<int>(largeStorageBox->Health);
						int maxHealth = static_cast<int>(largeStorageBox->GetMaxHealth());

						DrawTextY(hud, std::to_wstring(health).append(XOR(L"/")).append(std::to_wstring(maxHealth)).c_str(), screenPos.X, screenPos.Y, 1, color, font, m_textScale, &yPos);
					}
				}*/

				// Death Cache
				if (m_itemCache && FNameCache::IsA(currentActor, FNameCache::Containers::DeathItemCache))
				{
					auto largeStorageBox = static_cast<CG::DeathItemCache::ADeathItemCache_C*>(currentActor);
					CG::CoreUObject::FVector2D screenPos = Renderer::WorldToScreen(currentActor->K2_GetActorLocation(), camLoc, camRot, camFov);
					float yPos = screenPos.Y;
					DrawTextY(hud, largeStorageBox->DescriptiveName.ToWString().c_str(), screenPos.X, screenPos.Y, 1, { color.R, color.G, color.B, 1.f }, font, m_textScale, &yPos);
				}

				// Tek Sensor
				/*if (m_tekSensor && currentActor->IsA(CG::AStructure_TekAlarm_C::StaticClass()))
				{
					auto tekSensor = static_cast<CG::AStructure_TekAlarm_C*>(currentActor);
					CG::CoreUObject::FVector2D screenPos = Renderer::WorldToScreen(currentActor->K2_GetActorLocation(), camLoc, camRot, camFov);
					float yPos = screenPos.Y;
					DrawTextY(hud, tekSensor->DescriptiveName.ToStringW().c_str(), screenPos.X, screenPos.Y, 1, { color.R, color.G, color.B, 1.f }, font, m_textScale, &yPos);

					if (!m_hideStructureHealth)
					{
						int health = static_cast<int>(tekSensor->Health);
						int maxHealth = static_cast<int>(tekSensor->GetMaxHealth());

						DrawTextY(hud, std::to_wstring(health).append(XOR(L"/")).append(std::to_wstring(maxHealth)).c_str(), screenPos.X, screenPos.Y, 1, color, font, m_textScale, &yPos);
					}
				}*/

				// Cryo Fridge
				if (m_cryoFridge && FNameCache::IsA(currentActor, FNameCache::Containers::CryoFridge))
				{
					auto cryoFridge = static_cast<CG::CryoFridge::ACryoFridge_C*>(currentActor);

					int itemCount = cryoFridge->CurrentItemCount;
					int maxItemCount = cryoFridge->MaxItemCount;

					if (m_hideEmpty && itemCount <= 0) continue;

					CG::CoreUObject::FVector2D screenPos = Renderer::WorldToScreen(currentActor->K2_GetActorLocation(), camLoc, camRot, camFov);
					float yPos = screenPos.Y;
					DrawTextY(hud, cryoFridge->DescriptiveName.ToWString().c_str(), screenPos.X, screenPos.Y, 1, { color.R, color.G, color.B, 1.f }, font, m_textScale, &yPos);

					if (!m_hideItemAmount)
					{
						DrawTextY(hud, std::to_wstring(itemCount).append(XOR(L"/")).append(std::to_wstring(maxItemCount)).c_str(), screenPos.X, screenPos.Y, 1, color, font, m_textScale, &yPos);
					}

					if (!m_hideStructureHealth)
					{
						int health = static_cast<int>(cryoFridge->Health);
						int maxHealth = static_cast<int>(cryoFridge->GetMaxHealth());

						DrawTextY(hud, std::to_wstring(health).append(XOR(L"/")).append(std::to_wstring(maxHealth)).c_str(), screenPos.X, screenPos.Y, 1, color, font, m_textScale, &yPos);
					}
				}

				// Loadout Dummy
				/*if (m_loadoutDummy && currentActor->IsA(CG::AStructure_LoadoutDummy_C::StaticClass()))
				{
					auto loadoutDummy = static_cast<CG::AStructure_LoadoutDummy_C*>(currentActor);

					int itemCount = loadoutDummy->CurrentItemCount;
					int maxItemCount = loadoutDummy->MaxItemCount;

					if (m_hideEmpty && itemCount <= 0) continue;

					CG::CoreUObject::FVector2D screenPos = Renderer::WorldToScreen(currentActor->K2_GetActorLocation(), camLoc, camRot, camFov);
					float yPos = screenPos.Y;
					DrawTextY(hud, loadoutDummy->DescriptiveName.ToStringW().c_str(), screenPos.X, screenPos.Y, 1, { color.R, color.G, color.B, 1.f }, font, m_textScale, &yPos);

					if (!m_hideItemAmount)
					{
						DrawTextY(hud, std::to_wstring(itemCount).append(XOR(L"/")).append(std::to_wstring(maxItemCount)).c_str(), screenPos.X, screenPos.Y, 1, color, font, m_textScale, &yPos);
					}

					if (!m_hideStructureHealth)
					{
						int health = static_cast<int>(loadoutDummy->Health);
						int maxHealth = static_cast<int>(loadoutDummy->GetMaxHealth());

						DrawTextY(hud, std::to_wstring(health).append(XOR(L"/")).append(std::to_wstring(maxHealth)).c_str(), screenPos.X, screenPos.Y, 1, color, font, m_textScale, &yPos);
					}
				}*/
			}

			// Eggs
			if (m_eggs && FNameCache::IsA(currentActor, FNameCache::Actors::DroppedItemEgg))
			{
				auto egg = static_cast<CG::ShooterGame::ADroppedItemEgg*>(currentActor);

				CG::CoreUObject::FVector2D screenPos = Renderer::WorldToScreen(currentActor->K2_GetActorLocation(), camLoc, camRot, camFov);
				float yPos = screenPos.Y;
				DrawTextY(hud, Global::ConvertCharPtrToWstring(egg->MyItem->DescriptiveNameBase.ToString().c_str()).c_str(), screenPos.X, screenPos.Y, 1, { color.R, color.G, color.B, 1.f }, font, m_textScale, &yPos);
			}

			// Dropped Items
			if (m_item && FNameCache::IsA(currentActor, FNameCache::Actors::DroppedItemGeneric))
			{
				auto droppedItem = static_cast<CG::DroppedItemGeneric::ADroppedItemGeneric_C*>(currentActor);
				CG::CoreUObject::FVector2D screenPos = Renderer::WorldToScreen(currentActor->K2_GetActorLocation(), camLoc, camRot, camFov);
				float yPos = screenPos.Y;
				DrawTextY(hud, Global::ConvertCharPtrToWstring(droppedItem->MyItem->DescriptiveNameBase.ToString().c_str()).c_str(), screenPos.X, screenPos.Y, 1, { color.R, color.G, color.B, 1.f }, font, m_textScale, &yPos);
			}

			if (!m_exRender && !m_hideEmpty && m_loadoutDummy && m_sleepingPlayer)
			{
				Global::EnablePrivate = true;
			}
			else
			{
				Global::EnablePrivate = false;
			}
		}
	}

	void ModuleESP::LoadSettings(nlohmann::json config)
	{
		Module::LoadSettings(config);
		m_friendlyPlayer = GetCheckboxStateById(this->GetName(), 1, config);
		m_enemyPlayer = GetCheckboxStateById(this->GetName(), 2, config);
		m_sleepingPlayer = GetCheckboxStateById(this->GetName(), 3, config);
		m_deadPlayer = GetCheckboxStateById(this->GetName(), 4, config);
		m_extendedPlayerInfo = GetCheckboxStateById(this->GetName(), 5, config);
		m_friendlyDino = GetCheckboxStateById(this->GetName(), 6, config);
		m_enemyDino = GetCheckboxStateById(this->GetName(), 7, config);
		m_deadDino = GetCheckboxStateById(this->GetName(), 8, config);
		m_wildDino = GetCheckboxStateById(this->GetName(), 9, config);
		m_alphaDino = GetCheckboxStateById(this->GetName(), 10, config);
		m_vault = GetCheckboxStateById(this->GetName(), 11, config);
		m_dedicatedStorage = GetCheckboxStateById(this->GetName(), 12, config);
		// m_storageBox = GetCheckboxStateById(this->GetName(), 13);
		m_supplyCrate = GetCheckboxStateById(this->GetName(), 14, config);
		m_item = GetCheckboxStateById(this->GetName(), 15, config);
		m_itemCache = GetCheckboxStateById(this->GetName(), 16, config);
		m_hideHealth = GetCheckboxStateById(this->GetName(), 17, config);
		m_hideTribe = GetCheckboxStateById(this->GetName(), 18, config);
		m_hideAggression = GetCheckboxStateById(this->GetName(), 19, config);

		m_loadoutDummy = GetCheckboxStateById(this->GetName(), 20, config);
		m_storageBoxSmall = GetCheckboxStateById(this->GetName(), 21, config);
		m_storageBoxLarge = GetCheckboxStateById(this->GetName(), 22, config);
		m_turret = GetCheckboxStateById(this->GetName(), 23, config);
		m_bed = GetCheckboxStateById(this->GetName(), 24, config);
		m_tekGenerator = GetCheckboxStateById(this->GetName(), 25, config);
		m_electricGenerator = GetCheckboxStateById(this->GetName(), 26, config);
		m_cryoFridge = GetCheckboxStateById(this->GetName(), 27, config);

		m_hideEmpty = GetCheckboxStateById(this->GetName(), 28, config);

		m_hideEnemyStructures = GetCheckboxStateById(this->GetName(), 29, config);
		m_hideFriendlyStructures = GetCheckboxStateById(this->GetName(), 30, config);
		m_hideTurretSettings = GetCheckboxStateById(this->GetName(), 31, config);

		m_tekSensor = GetCheckboxStateById(this->GetName(), 32, config);

		m_hideStructureHealth = GetCheckboxStateById(this->GetName(), 33, config);
		m_showSpawnpoints = GetCheckboxStateById(this->GetName(), 34, config);
		m_notes = GetCheckboxStateById(this->GetName(), 35, config);
		m_ammoBox = GetCheckboxStateById(this->GetName(), 36, config);
		m_unlocked = GetCheckboxStateById(this->GetName(), 37, config);
		m_exRender = GetCheckboxStateById(this->GetName(), 38, config);
		m_eggs = GetCheckboxStateById(this->GetName(), 39, config);
		m_levelFilter = GetCheckboxStateById(this->GetName(), 40, config);
		m_hideDinoLevel = GetCheckboxStateById(this->GetName(), 41, config);

		m_resourceEsp = GetCheckboxStateById(this->GetName(), 42, config);
		m_metal = GetCheckboxStateById(this->GetName(), 43, config);
		m_oil = GetCheckboxStateById(this->GetName(), 44, config);
		m_crystal = GetCheckboxStateById(this->GetName(), 45, config);
		m_obsidian = GetCheckboxStateById(this->GetName(), 46, config);
		m_silica = GetCheckboxStateById(this->GetName(), 47, config);
		// m_silk = GetCheckboxStateById(this->GetName(), 48, config);

		m_stamina = GetCheckboxStateById(this->GetName(), 48, config);
		m_oxygen = GetCheckboxStateById(this->GetName(), 49, config);
		m_food = GetCheckboxStateById(this->GetName(), 50, config);
		m_weight = GetCheckboxStateById(this->GetName(), 51, config);
		m_meleeDamage = GetCheckboxStateById(this->GetName(), 52, config);
		m_movementSpeed = GetCheckboxStateById(this->GetName(), 53, config);
		m_torpor = GetCheckboxStateById(this->GetName(), 54, config);
		m_dinoNameFilter = GetCheckboxStateById(this->GetName(), 55, config);

		m_structureColor = GetColorValueById(this->GetName(), 1, config);
		m_dinoColor = GetColorValueById(this->GetName(), 2, config);
		m_enemyDinoColor = GetColorValueById(this->GetName(), 3, config);
		m_playerColor = GetColorValueById(this->GetName(), 4, config);
		m_enemyPlayerColor = GetColorValueById(this->GetName(), 5, config);
		m_healthColor = GetColorValueById(this->GetName(), 6, config);
		m_dinoStatsColor = GetColorValueById(this->GetName(), 7, config);

		m_textScale = static_cast<float>(GetSliderCurrentValueById(this->GetName(), 1, config) * 0.1f);
		m_levelSlider = GetSliderCurrentValueById(this->GetName(), 2, config);
		m_resourceRenderDistance = GetSliderCurrentValueById(this->GetName(), 3, config);
		m_resourceRenderReload = GetSliderCurrentValueById(this->GetName(), 4, config);
		m_espRange = static_cast<int>(GetSliderCurrentValueById(this->GetName(), 5, config));

		m_textboxDinoNameFilter = GetTextboxCurrentValueById(this->GetName(), 1, config);
		m_textboxDinoNameFilter2 = GetTextboxCurrentValueById(this->GetName(), 2, config);
		m_textboxDinoNameFilter3 = GetTextboxCurrentValueById(this->GetName(), 3, config);
		m_textboxDinoNameFilter4 = GetTextboxCurrentValueById(this->GetName(), 4, config);
		m_textboxDinoNameFilter5 = GetTextboxCurrentValueById(this->GetName(), 5, config);
		m_textboxDinoNameFilter6 = GetTextboxCurrentValueById(this->GetName(), 6, config);
		m_textboxDinoNameFilter7 = GetTextboxCurrentValueById(this->GetName(), 7, config);
		m_textboxDinoNameFilter8 = GetTextboxCurrentValueById(this->GetName(), 8, config);
	}
}
